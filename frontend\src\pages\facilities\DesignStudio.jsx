import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Link } from 'react-router-dom';

const DesignStudio = () => {
  useEffect(() => {
    document.title = 'Design Studio - KJL Industries';
  }, []);

  const features = [
    {
      title: '3D Modeling Workstations',
      description: 'High-performance workstations with advanced CAD software for precision design',
      icon: '💻'
    },
    {
      title: 'Virtual Reality Lab',
      description: 'Immersive VR environment for design visualization and virtual prototyping',
      icon: '🥽'
    },
    {
      title: 'Prototyping Workshop',
      description: '3D printing and rapid prototyping capabilities for quick design validation',
      icon: '🖨️'
    },
    {
      title: 'Collaborative Design Space',
      description: 'Open workspace designed for team collaboration and creative brainstorming',
      icon: '👥'
    },
    {
      title: 'Simulation Software',
      description: 'Advanced FEA and CFD simulation tools for performance analysis',
      icon: '📐'
    },
    {
      title: 'Digital Twin Technology',
      description: 'Real-time digital replicas for predictive maintenance and optimization',
      icon: '🔄'
    }
  ];

  const specifications = [
    { label: 'Design Workstations', value: '20 stations' },
    { label: 'VR Systems', value: '4 systems' },
    { label: '3D Printers', value: '8 printers' },
    { label: 'Collaboration Rooms', value: '6 rooms' },
    { label: 'Software Licenses', value: '50+ licenses' },
    { label: 'Design Capacity', value: '100+ projects' }
  ];

  const designCapabilities = [
    'Mechanical Design',
    'Industrial Design',
    'Product Development',
    'Concept Visualization',
    'Technical Documentation',
    'Design Optimization',
    'Ergonomic Analysis',
    'Material Selection',
    'Manufacturing Feasibility',
    'Cost Analysis'
  ];

  const softwareTools = [
    { name: 'SolidWorks', category: 'CAD' },
    { name: 'AutoCAD', category: 'CAD' },
    { name: 'ANSYS', category: 'Simulation' },
    { name: 'MATLAB', category: 'Analysis' },
    { name: 'KeyShot', category: 'Rendering' },
    { name: 'Unity', category: 'VR/AR' }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Back Button */}
      <div className="container-custom py-6">
        <Link 
          to="/facilities" 
          className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-300 group"
        >
          <svg 
            className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Facilities
        </Link>
      </div>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-white via-gray-50 to-gray-100">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🎨</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-display"
                style={{
                  background: 'linear-gradient(135deg, #1f2937 0%, #374151 30%, #6b7280 70%, #9ca3af 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                }}>
              Design Studio
            </h1>
            <p className="text-gray-700 text-lg md:text-xl leading-relaxed max-w-3xl mx-auto">
              Our modern design studio is the creative heart of KJL Industries, where innovative 
              ideas transform into cutting-edge engineering solutions through advanced design tools and collaborative workflows.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Specifications Grid */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Studio Specifications</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Resources and capabilities available in our design studio
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {specifications.map((spec, index) => (
              <motion.div
                key={spec.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6 text-center hover:bg-gray-100 transition-colors duration-300"
              >
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2 font-display">
                  {spec.value}
                </h3>
                <p className="text-gray-600 font-medium">{spec.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 font-display mb-4">
              Advanced Design Capabilities
            </h2>
            <p className="text-gray-700 text-lg max-w-3xl mx-auto">
              Our design studio combines cutting-edge technology with creative expertise 
              to deliver innovative solutions that push the boundaries of engineering design.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ 
                  scale: 1.03,
                  y: -5
                }}
                className="bg-white rounded-2xl p-8 shadow-soft hover:shadow-medium transition-all duration-300 border border-gray-200 group"
              >
                <div className="text-4xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-display mb-4 group-hover:text-gray-700 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Software Tools */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Professional Software Tools</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Industry-leading software solutions for comprehensive design and analysis
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {softwareTools.map((tool, index) => (
              <motion.div
                key={tool.name}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 text-center hover:from-gray-100 hover:to-gray-200 transition-all duration-300 border border-gray-200"
              >
                <h3 className="text-xl font-bold text-gray-900 mb-2">{tool.name}</h3>
                <span className="inline-block px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-sm font-medium">
                  {tool.category}
                </span>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Capabilities */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Design Services</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Comprehensive design services from concept to production-ready solutions
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
            {designCapabilities.map((capability, index) => (
              <motion.div
                key={capability}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                viewport={{ once: true }}
                className="bg-white rounded-lg p-4 text-center hover:bg-gray-50 transition-all duration-300 border border-gray-200 shadow-soft"
              >
                <p className="text-gray-700 font-medium text-sm">{capability}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why It Matters Section */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 md:p-12"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mr-4">
                  <span className="text-2xl">💡</span>
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 font-display">Why It Matters</h2>
              </div>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                Our design studio is where innovation comes to life. By centralizing our design capabilities 
                and fostering collaboration between engineers, designers, and stakeholders, we accelerate 
                development cycles and ensure that every solution is optimized for performance, manufacturability, and cost-effectiveness.
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-soft">
                  <h3 className="font-bold text-gray-900 mb-2">Innovation & Creativity</h3>
                  <p className="text-gray-600">
                    Advanced tools and collaborative environment foster innovative thinking 
                    and creative problem-solving approaches.
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-soft">
                  <h3 className="font-bold text-gray-900 mb-2">Speed to Market</h3>
                  <p className="text-gray-600">
                    Rapid prototyping and virtual validation capabilities significantly 
                    reduce development time and accelerate product launches.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-gray-100 to-gray-200">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">
              Ready to Bring Your Ideas to Life?
            </h2>
            <p className="text-gray-700 text-lg mb-8 max-w-2xl mx-auto">
              Contact us to learn more about our design services and how we can help transform your concepts into reality.
            </p>
            <Link 
              to="/contact"
              className="inline-flex items-center px-8 py-4 bg-gray-900 text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
            >
              Start Your Project
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default DesignStudio;
