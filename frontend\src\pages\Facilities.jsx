import React from 'react';
import { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const Facilities = () => {
  const facilities = [
    {
      title: 'Manufacturing Plant',
      description: 'State-of-the-art manufacturing facility equipped with the latest technology and machinery.',
      features: [
        'Advanced CNC Machines',
        'Automated Assembly Lines',
        'Quality Control Lab',
        'Research & Development Center',
      ],
      image: '/images/facilities/manufacturing.jpg',
    },
    {
      title: 'Testing Laboratory',
      description: 'Comprehensive testing facility for ensuring product quality and reliability.',
      features: [
        'Material Testing Equipment',
        'Environmental Testing Chambers',
        'Precision Measurement Tools',
        'Performance Analysis Systems',
      ],
      image: '/images/facilities/testing.jpg',
    },
    {
      title: 'Design Studio',
      description: 'Modern design studio for innovative product development and engineering solutions.',
      features: [
        '3D Modeling Workstations',
        'Virtual Reality Lab',
        'Prototyping Workshop',
        'Collaborative Design Space',
      ],
      image: '/images/facilities/design.jpg',
    },
  ];

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 font-display">Our Facilities</h1>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              Experience our world-class facilities equipped with cutting-edge technology
              and staffed by industry experts.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Facilities Sections */}
      {facilities.map((facility, index) => (
        <FacilitySection
          key={facility.title}
          facility={facility}
          index={index}
        />
      ))}
    </div>
  );
};

// Removed TypeScript interface - facility shape is now implicit
// interface FacilityProps {
//   facility: {
//     title: string;
//     description: string;
//     features: string[];
//     image: string;
//   };
//   index: number;
// }

const FacilitySection = ({ facility, index }) => { // Removed TypeScript type annotation: FacilityProps
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  return (
    <section
      ref={ref}
      className={`py-20 ${
        index % 2 === 0 ? 'bg-white' : 'bg-gray-100'
      }`}
    >
      <div className="container-custom">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className={`grid md:grid-cols-2 gap-12 items-center ${
            index % 2 === 0 ? '' : 'md:grid-flow-dense'
          }`}
        >
          {/* Image Section */}
          <motion.div
            variants={itemVariants}
            className="relative h-80 rounded-lg overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-gray-500/20 to-gray-300/20">
              {/* Replace with actual image */}
              <div className="w-full h-full bg-gray-300 shadow-inner" />
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h2 className="text-3xl font-bold text-gray-900 font-display">{facility.title}</h2>
            <p className="text-gray-600 leading-relaxed">{facility.description}</p>
            <ul className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {facility.features.map((feature) => (
                <motion.li
                  key={feature}
                  variants={itemVariants}
                  className="flex items-center space-x-2"
                >
                  <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="text-gray-700">{feature}</span>
                </motion.li>
              ))}
            </ul>
            <motion.button
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn btn-primary"
            >
              Learn More
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Facilities;
