import React from 'react';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const Customers = () => {
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      company: 'Tech Solutions Inc',
      text: 'KJL Industries has been instrumental in our success...',
      image: '/images/testimonial1.jpg'
    },
    {
      name: '<PERSON>',
      position: 'Operations Director, Global Manufacturing',
      content: 'The quality and precision of KJL\'s engineering work is unmatched. They\'ve consistently delivered beyond our expectations.',
      image: '/images/testimonials/sarah.jpg',
    },
    {
      name: '<PERSON>',
      position: 'Head of Production, Asian Motors',
      content: 'Working with KJL Industries has transformed our production capabilities. Their expertise and commitment to excellence are truly remarkable.',
      image: '/images/testimonials/michael.jpg',
    },
  ];

  const clients = [
    { name: 'TechCorp', logo: '/images/clients/techcorp.png' },
    { name: 'Global Manufacturing', logo: '/images/clients/global.png' },
    { name: 'Asian Motors', logo: '/images/clients/asian.png' },
    { name: 'Industrial Solutions', logo: '/images/clients/industrial.png' },
    { name: 'Engineering Plus', logo: '/images/clients/engineering.png' },
    { name: 'Precision Works', logo: '/images/clients/precision.png' },
  ];

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 font-display">Our Customers</h1>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              Trusted by leading companies worldwide to deliver exceptional engineering solutions.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="container-custom">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-center mb-12 text-gray-900 font-display"
          >
            What Our Clients Say
          </motion.h2>

          <div className="relative">
            <div className="max-w-3xl mx-auto">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTestimonial}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.5 }}
                  className="bg-white rounded-lg p-8 text-center shadow-medium border border-gray-200"
                >
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full overflow-hidden bg-gray-200">
                    {/* Replace with actual image */}
                    <div className="w-full h-full bg-gray-200" />
                  </div>
                  <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                    "{testimonials[activeTestimonial].content}"
                  </p>
                  <h3 className="font-bold text-xl mb-2 text-gray-900 font-display">
                    {testimonials[activeTestimonial].name}
                  </h3>
                  <p className="text-gray-500 font-medium">
                    {testimonials[activeTestimonial].position}
                  </p>
                </motion.div>
              </AnimatePresence>

              {/* Navigation Dots */}
              <div className="flex justify-center space-x-2 mt-6">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                      activeTestimonial === index ? 'bg-gray-900' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Clients Grid */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-100">
        <div className="container-custom">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-center mb-12 text-gray-900 font-display"
          >
            Trusted by Industry Leaders
          </motion.h2>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {clients.map((client, index) => (
              <motion.div
                key={client.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center justify-center"
              >
                <div className="w-32 h-32 bg-white rounded-lg p-4 hover:bg-gray-50 transition-all duration-300 shadow-soft hover:shadow-medium border border-gray-200">
                  {/* Replace with actual logo */}
                  <div className="w-full h-full bg-gray-200 rounded" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center max-w-2xl mx-auto"
          >
            <h2 className="text-3xl font-bold mb-6">Ready to Work Together?</h2>
            <p className="text-gray-400 mb-8">
              Join our growing list of satisfied customers and experience engineering excellence.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn btn-primary"
            >
              Contact Us Today
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Customers;
