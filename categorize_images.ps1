# KJL Industries Image Categorization Script
# This script helps move images to appropriate facility categories

Write-Host "🏭 KJL Industries Image Categorization Tool" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Function to move images to a specific category
function Move-ImagesToCategory {
    param(
        [string]$SourceFolder,
        [string]$Category,
        [string[]]$ImageNumbers
    )
    
    $categoryPath = Join-Path $SourceFolder $Category
    
    if (-not (Test-Path $categoryPath)) {
        Write-Host "❌ Category folder '$Category' not found in '$SourceFolder'" -ForegroundColor Red
        return
    }
    
    $movedCount = 0
    foreach ($imageNum in $ImageNumbers) {
        # Handle both folder 1 (DEV_99xx) and folder 2 (DEV_00xx) naming patterns
        if ($SourceFolder -eq "1") {
            $imageName = "DEV_99$imageNum.JPG"
        } else {
            $imageName = "DEV_00$imageNum.JPG"
        }
        
        $sourcePath = Join-Path $SourceFolder $imageName
        $destPath = Join-Path $categoryPath $imageName
        
        if (Test-Path $sourcePath) {
            try {
                Move-Item $sourcePath $destPath -Force
                Write-Host "✅ Moved $imageName to $Category" -ForegroundColor Green
                $movedCount++
            } catch {
                Write-Host "❌ Failed to move $imageName : $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠️  Image $imageName not found" -ForegroundColor Yellow
        }
    }
    
    Write-Host "📊 Moved $movedCount images to $Category in folder $SourceFolder" -ForegroundColor Cyan
}

# Function to list images in a folder
function Show-ImagesInFolder {
    param([string]$FolderPath)
    
    Write-Host "`n📁 Images in folder '$FolderPath':" -ForegroundColor Yellow
    $images = Get-ChildItem -Path $FolderPath -Filter "*.JPG" | Sort-Object Name
    
    if ($images.Count -eq 0) {
        Write-Host "   No JPG images found" -ForegroundColor Gray
    } else {
        $images | ForEach-Object { Write-Host "   $($_.Name)" -ForegroundColor White }
        Write-Host "   Total: $($images.Count) images" -ForegroundColor Cyan
    }
}

# Function to show categorization status
function Show-CategorizationStatus {
    Write-Host "`n📊 CATEGORIZATION STATUS" -ForegroundColor Magenta
    Write-Host "========================" -ForegroundColor Magenta
    
    foreach ($folder in @("1", "2")) {
        Write-Host "`nFolder $folder:" -ForegroundColor Yellow
        
        # Count uncategorized images
        $uncategorized = (Get-ChildItem -Path $folder -Filter "*.JPG").Count
        Write-Host "  Uncategorized: $uncategorized images" -ForegroundColor White
        
        # Count categorized images
        foreach ($category in @("manufacturing-plant", "testing-laboratory", "design-studio")) {
            $categoryPath = Join-Path $folder $category
            $count = (Get-ChildItem -Path $categoryPath -Filter "*.JPG" -ErrorAction SilentlyContinue).Count
            Write-Host "  $category : $count images" -ForegroundColor Green
        }
    }
}

# Main menu
do {
    Write-Host "`n🎯 CATEGORIZATION MENU" -ForegroundColor Cyan
    Write-Host "=====================" -ForegroundColor Cyan
    Write-Host "1. Show current status"
    Write-Host "2. List images in folder 1"
    Write-Host "3. List images in folder 2"
    Write-Host "4. Move images to Manufacturing Plant"
    Write-Host "5. Move images to Testing Laboratory"
    Write-Host "6. Move images to Design Studio"
    Write-Host "7. Example: Move specific images"
    Write-Host "0. Exit"
    
    $choice = Read-Host "`nEnter your choice (0-7)"
    
    switch ($choice) {
        "1" { Show-CategorizationStatus }
        "2" { Show-ImagesInFolder "1" }
        "3" { Show-ImagesInFolder "2" }
        "4" {
            $folder = Read-Host "Enter folder number (1 or 2)"
            $images = Read-Host "Enter image numbers separated by commas (e.g., 75,76,77 for folder 1 or 01,02,03 for folder 2)"
            $imageArray = $images -split "," | ForEach-Object { $_.Trim() }
            Move-ImagesToCategory $folder "manufacturing-plant" $imageArray
        }
        "5" {
            $folder = Read-Host "Enter folder number (1 or 2)"
            $images = Read-Host "Enter image numbers separated by commas (e.g., 75,76,77 for folder 1 or 01,02,03 for folder 2)"
            $imageArray = $images -split "," | ForEach-Object { $_.Trim() }
            Move-ImagesToCategory $folder "testing-laboratory" $imageArray
        }
        "6" {
            $folder = Read-Host "Enter folder number (1 or 2)"
            $images = Read-Host "Enter image numbers separated by commas (e.g., 75,76,77 for folder 1 or 01,02,03 for folder 2)"
            $imageArray = $images -split "," | ForEach-Object { $_.Trim() }
            Move-ImagesToCategory $folder "design-studio" $imageArray
        }
        "7" {
            Write-Host "`n📝 EXAMPLE USAGE:" -ForegroundColor Yellow
            Write-Host "For folder 1 (DEV_99xx.JPG files):"
            Write-Host "  Enter image numbers like: 75,76,77,78"
            Write-Host "  This will move DEV_9975.JPG, DEV_9976.JPG, etc."
            Write-Host ""
            Write-Host "For folder 2 (DEV_00xx.JPG files):"
            Write-Host "  Enter image numbers like: 01,02,03,04"
            Write-Host "  This will move DEV_0001.JPG, DEV_0002.JPG, etc."
        }
        "0" { 
            Write-Host "👋 Goodbye! Happy categorizing!" -ForegroundColor Green
            break
        }
        default { Write-Host "❌ Invalid choice. Please try again." -ForegroundColor Red }
    }
} while ($true)
