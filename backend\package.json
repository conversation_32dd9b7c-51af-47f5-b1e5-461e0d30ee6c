{"name": "kjl-industries-backend", "version": "1.0.0", "description": "Backend server for KJL Industries website", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "watch": "tsc -w", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["engineering", "industrial", "manufacturing"], "author": "KJL Industries", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/node": "^22.15.17", "@types/nodemailer": "^6.4.17", "nodemon": "^2.0.22", "dotenv": "^16.5.0", "express": "^5.1.0", "nodemailer": "^7.0.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}