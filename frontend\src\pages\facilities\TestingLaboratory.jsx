import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Link } from 'react-router-dom';

const TestingLaboratory = () => {
  useEffect(() => {
    document.title = 'Testing Laboratory - KJL Industries';
  }, []);

  const features = [
    {
      title: 'Material Testing Equipment',
      description: 'Advanced tensile, compression, and fatigue testing machines for material analysis',
      icon: '🔬'
    },
    {
      title: 'Environmental Testing Chambers',
      description: 'Climate-controlled chambers for temperature, humidity, and corrosion testing',
      icon: '🌡️'
    },
    {
      title: 'Precision Measurement Tools',
      description: 'High-accuracy coordinate measuring machines and optical inspection systems',
      icon: '📏'
    },
    {
      title: 'Performance Analysis Systems',
      description: 'Real-time monitoring and data analysis for comprehensive performance evaluation',
      icon: '📊'
    },
    {
      title: 'Non-Destructive Testing',
      description: 'Ultrasonic, X-ray, and magnetic particle testing for internal defect detection',
      icon: '🔍'
    },
    {
      title: 'Calibration Services',
      description: 'NIST-traceable calibration services for measurement equipment and instruments',
      icon: '⚖️'
    }
  ];

  const specifications = [
    { label: 'Testing Stations', value: '15 stations' },
    { label: 'Environmental Chambers', value: '8 chambers' },
    { label: 'Temperature Range', value: '-70°C to +200°C' },
    { label: 'Humidity Range', value: '10% to 95% RH' },
    { label: 'Measurement Accuracy', value: '±0.001mm' },
    { label: 'Certification Standards', value: 'ISO 17025' }
  ];

  const testingCapabilities = [
    'Mechanical Properties Testing',
    'Thermal Analysis',
    'Chemical Composition Analysis',
    'Dimensional Inspection',
    'Surface Roughness Measurement',
    'Hardness Testing',
    'Vibration Analysis',
    'Electrical Testing',
    'Corrosion Resistance Testing',
    'Fatigue Life Assessment'
  ];

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Back Button */}
      <div className="container-custom py-6">
        <Link 
          to="/facilities" 
          className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-300 group"
        >
          <svg 
            className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Facilities
        </Link>
      </div>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-white via-gray-50 to-gray-100">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🧪</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-display"
                style={{
                  background: 'linear-gradient(135deg, #1f2937 0%, #374151 30%, #6b7280 70%, #9ca3af 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                }}>
              Testing Laboratory
            </h1>
            <p className="text-gray-700 text-lg md:text-xl leading-relaxed max-w-3xl mx-auto">
              Our comprehensive testing laboratory ensures product quality and reliability through 
              rigorous testing protocols and advanced analytical equipment.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Specifications Grid */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Laboratory Specifications</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Technical capabilities and certifications of our testing facility
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {specifications.map((spec, index) => (
              <motion.div
                key={spec.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6 text-center hover:bg-gray-100 transition-colors duration-300"
              >
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2 font-display">
                  {spec.value}
                </h3>
                <p className="text-gray-600 font-medium">{spec.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 font-display mb-4">
              Advanced Testing Equipment
            </h2>
            <p className="text-gray-700 text-lg max-w-3xl mx-auto">
              Our laboratory is equipped with state-of-the-art testing equipment and 
              analytical instruments to ensure comprehensive quality assurance.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ 
                  scale: 1.03,
                  y: -5
                }}
                className="bg-white rounded-2xl p-8 shadow-soft hover:shadow-medium transition-all duration-300 border border-gray-200 group"
              >
                <div className="text-4xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-display mb-4 group-hover:text-gray-700 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testing Capabilities */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Testing Capabilities</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Comprehensive range of testing services to validate product performance and quality
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
            {testingCapabilities.map((capability, index) => (
              <motion.div
                key={capability}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 text-center hover:from-gray-100 hover:to-gray-200 transition-all duration-300 border border-gray-200"
              >
                <p className="text-gray-700 font-medium text-sm">{capability}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why It Matters Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 md:p-12 shadow-soft border border-gray-200"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mr-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 font-display">Why It Matters</h2>
              </div>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                Our testing laboratory is the cornerstone of quality assurance at KJL Industries. 
                Through rigorous testing protocols and advanced analytical capabilities, we ensure 
                that every product meets or exceeds industry standards and customer expectations.
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-soft border border-gray-100">
                  <h3 className="font-bold text-gray-900 mb-2">Quality Assurance</h3>
                  <p className="text-gray-600">
                    Comprehensive testing protocols ensure products meet stringent quality 
                    standards before reaching customers.
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-soft border border-gray-100">
                  <h3 className="font-bold text-gray-900 mb-2">Risk Mitigation</h3>
                  <p className="text-gray-600">
                    Early detection of potential issues through thorough testing reduces 
                    field failures and warranty claims.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-gray-100 to-gray-200">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">
              Need Testing Services for Your Products?
            </h2>
            <p className="text-gray-700 text-lg mb-8 max-w-2xl mx-auto">
              Contact us to learn more about our comprehensive testing capabilities and quality assurance services.
            </p>
            <Link 
              to="/contact"
              className="inline-flex items-center px-8 py-4 bg-gray-900 text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
            >
              Get Testing Quote
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default TestingLaboratory;
