import {
  Canvas,
  _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPointerEvents,
  createPortal,
  createRoot,
  dispose,
  extend,
  flushGlobalEffects,
  getRootState,
  invalidate,
  reconciler,
  threeTypes,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
} from "./chunk-2BOSGZEN.js";
import "./chunk-RMN3I2AX.js";
import "./chunk-4FNVBLPA.js";
import "./chunk-4Y3WHDJB.js";
import "./chunk-5WWUZCGV.js";
export {
  Canvas,
  threeTypes as ReactThreeFiber,
  _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPortal,
  createRoot,
  dispose,
  createPointerEvents as events,
  extend,
  flushGlobalEffects,
  getRootState,
  invalidate,
  reconciler,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
};
//# sourceMappingURL=@react-three_fiber.js.map
