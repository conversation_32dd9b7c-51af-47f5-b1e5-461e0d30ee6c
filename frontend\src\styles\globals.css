@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: light;
}

html {
  scroll-behavior: smooth;
}

body {
  @apply bg-gray-50 text-gray-900;
  font-family: 'Inter', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Hover effects */
.hover-scale {
  @apply transition-transform duration-300;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Button styles */
.btn {
  @apply px-6 py-2 rounded-md font-medium transition-all duration-300;
}

.btn-primary {
  @apply bg-gray-900 text-white hover:bg-gray-800 shadow-lg hover:shadow-xl;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg p-6 hover:bg-gray-50 transition-all duration-300 shadow-md hover:shadow-lg border border-gray-200;
}

/* Section spacing */
.section {
  @apply py-20;
}

/* Container */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Modern Typography */
.text-display {
  @apply font-display font-bold tracking-tight;
}

.text-heading {
  @apply font-display font-semibold text-gray-900;
}

.text-body {
  @apply text-gray-600 leading-relaxed;
}

.text-caption {
  @apply text-sm text-gray-500 font-medium;
}

/* Modern gradients */
.gradient-text {
  @apply bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent;
}

/* Enhanced focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

/* Scroll animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-soft {
  animation: pulse-soft 2s ease-in-out infinite;
}

/* Enhanced button styles */
.btn-gradient {
  @apply bg-gradient-to-r from-gray-900 to-gray-700 text-white hover:from-gray-800 hover:to-gray-600 shadow-lg hover:shadow-xl transition-all duration-300;
}

/* Glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

/* Elevated cards */
.card-elevated {
  @apply bg-white rounded-2xl shadow-medium hover:shadow-large transition-all duration-300 border border-gray-100;
}

/* Text effects */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
