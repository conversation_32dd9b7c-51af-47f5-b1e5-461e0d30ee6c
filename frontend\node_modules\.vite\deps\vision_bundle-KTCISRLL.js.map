{"version": 3, "sources": ["../../@mediapipe/tasks-vision/vision_bundle.mjs"], "sourcesContent": ["var t=\"undefined\"!=typeof self?self:{};function e(e,n){t:{for(var r=[\"CLOSURE_FLAGS\"],i=t,s=0;s<r.length;s++)if(null==(i=i[r[s]])){r=null;break t}r=i}return null!=(e=r&&r[e])?e:n}function n(){throw Error(\"Invalid UTF8\")}function r(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}let i,s;const o=\"undefined\"!=typeof TextDecoder;let a;const h=\"undefined\"!=typeof TextEncoder;function c(t){if(h)t=(a||=new TextEncoder).encode(t);else{let n=0;const r=new Uint8Array(3*t.length);for(let i=0;i<t.length;i++){var e=t.charCodeAt(i);if(e<128)r[n++]=e;else{if(e<2048)r[n++]=e>>6|192;else{if(e>=55296&&e<=57343){if(e<=56319&&i<t.length){const s=t.charCodeAt(++i);if(s>=56320&&s<=57343){e=1024*(e-55296)+s-56320+65536,r[n++]=e>>18|240,r[n++]=e>>12&63|128,r[n++]=e>>6&63|128,r[n++]=63&e|128;continue}i--}e=65533}r[n++]=e>>12|224,r[n++]=e>>6&63|128}r[n++]=63&e|128}}t=n===r.length?r:r.subarray(0,n)}return t}var u,l=e(610401301,!1),f=e(653718497,e(1,!0)),d=e(660014094,!1);const p=t.navigator;function g(t){return!!l&&(!!u&&u.brands.some((({brand:e})=>e&&-1!=e.indexOf(t))))}function m(e){var n;return(n=t.navigator)&&(n=n.userAgent)||(n=\"\"),-1!=n.indexOf(e)}function y(){return!!l&&(!!u&&u.brands.length>0)}function _(){return y()?g(\"Chromium\"):(m(\"Chrome\")||m(\"CriOS\"))&&!(!y()&&m(\"Edge\"))||m(\"Silk\")}function v(t){return v[\" \"](t),t}u=p&&p.userAgentData||null,v[\" \"]=function(){};var E=!y()&&(m(\"Trident\")||m(\"MSIE\"));!m(\"Android\")||_(),_(),m(\"Safari\")&&(_()||!y()&&m(\"Coast\")||!y()&&m(\"Opera\")||!y()&&m(\"Edge\")||(y()?g(\"Microsoft Edge\"):m(\"Edg/\"))||y()&&g(\"Opera\"));var w={},T=null;function A(t){var e=t.length,n=3*e/4;n%3?n=Math.floor(n):-1!=\"=.\".indexOf(t[e-1])&&(n=-1!=\"=.\".indexOf(t[e-2])?n-2:n-1);var r=new Uint8Array(n),i=0;return function(t,e){function n(e){for(;r<t.length;){var n=t.charAt(r++),i=T[n];if(null!=i)return i;if(!/^[\\s\\xa0]*$/.test(n))throw Error(\"Unknown base64 encoding at char: \"+n)}return e}b();for(var r=0;;){var i=n(-1),s=n(0),o=n(64),a=n(64);if(64===a&&-1===i)break;e(i<<2|s>>4),64!=o&&(e(s<<4&240|o>>2),64!=a&&e(o<<6&192|a))}}(t,(function(t){r[i++]=t})),i!==n?r.subarray(0,i):r}function b(){if(!T){T={};for(var t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\"),e=[\"+/=\",\"+/\",\"-_=\",\"-_.\",\"-_\"],n=0;n<5;n++){var r=t.concat(e[n].split(\"\"));w[n]=r;for(var i=0;i<r.length;i++){var s=r[i];void 0===T[s]&&(T[s]=i)}}}}var k=\"undefined\"!=typeof Uint8Array,S=!E&&\"function\"==typeof btoa;function x(t){if(!S){var e;void 0===e&&(e=0),b(),e=w[e];var n=Array(Math.floor(t.length/3)),r=e[64]||\"\";let h=0,c=0;for(;h<t.length-2;h+=3){var i=t[h],s=t[h+1],o=t[h+2],a=e[i>>2];i=e[(3&i)<<4|s>>4],s=e[(15&s)<<2|o>>6],o=e[63&o],n[c++]=a+i+s+o}switch(a=0,o=r,t.length-h){case 2:o=e[(15&(a=t[h+1]))<<2]||r;case 1:t=t[h],n[c]=e[t>>2]+e[(3&t)<<4|a>>4]+o+r}return n.join(\"\")}for(e=\"\",n=0,r=t.length-10240;n<r;)e+=String.fromCharCode.apply(null,t.subarray(n,n+=10240));return e+=String.fromCharCode.apply(null,n?t.subarray(n):t),btoa(e)}const L=/[-_.]/g,R={\"-\":\"+\",_:\"/\",\".\":\"=\"};function F(t){return R[t]||\"\"}function M(t){if(!S)return A(t);L.test(t)&&(t=t.replace(L,F)),t=atob(t);const e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}function I(t){return k&&null!=t&&t instanceof Uint8Array}var P={};let O;function C(t){if(t!==P)throw Error(\"illegal external caller\")}function N(){return O||=new D(null,P)}function U(t){C(P);var e=t.ba;return null==(e=null==e||I(e)?e:\"string\"==typeof e?M(e):null)?e:t.ba=e}var D=class{constructor(t,e){if(C(e),this.ba=t,null!=t&&0===t.length)throw Error(\"ByteString should be constructed with non-empty values\")}ua(){return new Uint8Array(U(this)||0)}};function B(t,e){t.__closure__error__context__984382||(t.__closure__error__context__984382={}),t.__closure__error__context__984382.severity=e}let G;function j(){const e=Error();B(e,\"incident\"),function(e){t.setTimeout((()=>{throw e}),0)}(e)}function V(t){return B(t=Error(t),\"warning\"),t}function X(){return\"function\"==typeof BigInt}function H(t){return Array.prototype.slice.call(t)}var W=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol();function z(t){return\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol()?Symbol():t}var K=z(),Y=z(\"0di\"),$=z(\"2ex\"),q=z(\"1oa\"),J=z(\"0dg\"),Z=W?(t,e)=>{t[K]|=e}:(t,e)=>{void 0!==t.G?t.G|=e:Object.defineProperties(t,{G:{value:e,configurable:!0,writable:!0,enumerable:!1}})},Q=W?(t,e)=>{t[K]&=~e}:(t,e)=>{void 0!==t.G&&(t.G&=~e)},tt=W?t=>0|t[K]:t=>0|t.G,et=W?t=>t[K]:t=>t.G,nt=W?(t,e)=>{t[K]=e}:(t,e)=>{void 0!==t.G?t.G=e:Object.defineProperties(t,{G:{value:e,configurable:!0,writable:!0,enumerable:!1}})};function rt(t){return Z(t,34),t}function it(t,e){nt(e,-14591&(0|t))}function st(t,e){nt(e,-14557&(34|t))}var ot,at={},ht={};function ct(t){return!(!t||\"object\"!=typeof t||t.La!==ht)}function ut(t){return null!==t&&\"object\"==typeof t&&!Array.isArray(t)&&t.constructor===Object}function lt(t,e,n){if(null!=t)if(\"string\"==typeof t)t=t?new D(t,P):N();else if(t.constructor!==D)if(I(t))t=t.length?new D(n?t:new Uint8Array(t),P):N();else{if(!e)throw Error();t=void 0}return t}function ft(t){return!(!Array.isArray(t)||t.length)&&!!(1&tt(t))}const dt=[];function pt(t){if(2&t)throw Error()}nt(dt,55),ot=Object.freeze(dt);class gt{constructor(t,e,n){this.l=0,this.g=t,this.h=e,this.m=n}next(){if(this.l<this.g.length){const t=this.g[this.l++];return{done:!1,value:this.h?this.h.call(this.m,t):t}}return{done:!0,value:void 0}}[Symbol.iterator](){return new gt(this.g,this.h,this.m)}}let mt;function yt(t,e){(e=mt?e[mt]:void 0)&&(t[mt]=H(e))}var _t=Object.freeze({});Object.freeze({});var vt=Object.freeze({});function Et(t){return t.Sa=!0,t}var wt=Et((t=>\"number\"==typeof t)),Tt=Et((t=>\"string\"==typeof t)),At=Et((t=>\"boolean\"==typeof t)),bt=\"function\"==typeof t.BigInt&&\"bigint\"==typeof t.BigInt(0),kt=Et((t=>bt?t>=xt&&t<=Rt:\"-\"===t[0]?Ft(t,St):Ft(t,Lt)));const St=Number.MIN_SAFE_INTEGER.toString(),xt=bt?BigInt(Number.MIN_SAFE_INTEGER):void 0,Lt=Number.MAX_SAFE_INTEGER.toString(),Rt=bt?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Ft(t,e){if(t.length>e.length)return!1;if(t.length<e.length||t===e)return!0;for(let n=0;n<t.length;n++){const r=t[n],i=e[n];if(r>i)return!1;if(r<i)return!0}}const Mt=\"function\"==typeof Uint8Array.prototype.slice;let It,Pt=0,Ot=0;function Ct(t){const e=t>>>0;Pt=e,Ot=(t-e)/4294967296>>>0}function Nt(t){if(t<0){Ct(-t);const[e,n]=Xt(Pt,Ot);Pt=e>>>0,Ot=n>>>0}else Ct(t)}function Ut(t){const e=It||=new DataView(new ArrayBuffer(8));e.setFloat32(0,+t,!0),Ot=0,Pt=e.getUint32(0,!0)}function Dt(t,e){return 4294967296*e+(t>>>0)}function Bt(t,e){const n=2147483648&e;return n&&(e=~e>>>0,0==(t=1+~t>>>0)&&(e=e+1>>>0)),t=Dt(t,e),n?-t:t}function Gt(t,e){if(t>>>=0,(e>>>=0)<=2097151)var n=\"\"+(4294967296*e+t);else X()?n=\"\"+(BigInt(e)<<BigInt(32)|BigInt(t)):(t=(16777215&t)+6777216*(n=16777215&(t>>>24|e<<8))+6710656*(e=e>>16&65535),n+=8147497*e,e*=2,t>=1e7&&(n+=t/1e7>>>0,t%=1e7),n>=1e7&&(e+=n/1e7>>>0,n%=1e7),n=e+jt(n)+jt(t));return n}function jt(t){return t=String(t),\"0000000\".slice(t.length)+t}function Vt(t){if(t.length<16)Nt(Number(t));else if(X())t=BigInt(t),Pt=Number(t&BigInt(4294967295))>>>0,Ot=Number(t>>BigInt(32)&BigInt(4294967295));else{const e=+(\"-\"===t[0]);Ot=Pt=0;const n=t.length;for(let r=e,i=(n-e)%6+e;i<=n;r=i,i+=6){const e=Number(t.slice(r,i));Ot*=1e6,Pt=1e6*Pt+e,Pt>=4294967296&&(Ot+=Math.trunc(Pt/4294967296),Ot>>>=0,Pt>>>=0)}if(e){const[t,e]=Xt(Pt,Ot);Pt=t,Ot=e}}}function Xt(t,e){return e=~e,t?t=1+~t:e+=1,[t,e]}function Ht(t){return null==t||\"number\"==typeof t?t:\"NaN\"===t||\"Infinity\"===t||\"-Infinity\"===t?Number(t):void 0}function Wt(t){return null==t||\"boolean\"==typeof t?t:\"number\"==typeof t?!!t:void 0}const zt=/^-?([1-9][0-9]*|0)(\\.[0-9]+)?$/;function Kt(t){const e=typeof t;switch(e){case\"bigint\":return!0;case\"number\":return Number.isFinite(t)}return\"string\"===e&&zt.test(t)}function Yt(t){if(null==t)return t;if(\"string\"==typeof t){if(!t)return;t=+t}return\"number\"==typeof t&&Number.isFinite(t)?0|t:void 0}function $t(t){if(null==t)return t;if(\"string\"==typeof t){if(!t)return;t=+t}return\"number\"==typeof t&&Number.isFinite(t)?t>>>0:void 0}function qt(t){return\"-\"!==t[0]&&(t.length<20||20===t.length&&Number(t.substring(0,6))<184467)}function Jt(t){return t=Math.trunc(t),Number.isSafeInteger(t)||(Nt(t),t=Bt(Pt,Ot)),t}function Zt(t){var e=Math.trunc(Number(t));if(Number.isSafeInteger(e))return String(e);if(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),!(\"-\"===t[0]?t.length<20||20===t.length&&Number(t.substring(0,7))>-922337:t.length<19||19===t.length&&Number(t.substring(0,6))<922337))if(Vt(t),t=Pt,2147483648&(e=Ot))if(X())t=\"\"+(BigInt(0|e)<<BigInt(32)|BigInt(t>>>0));else{const[n,r]=Xt(t,e);t=\"-\"+Gt(n,r)}else t=Gt(t,e);return t}function Qt(t){return null==t?t:\"bigint\"==typeof t?(kt(t)?t=Number(t):(t=BigInt.asIntN(64,t),t=kt(t)?Number(t):String(t)),t):Kt(t)?\"number\"==typeof t?Jt(t):Zt(t):void 0}function te(t){if(null==t)return t;var e=typeof t;if(\"bigint\"===e)return String(BigInt.asUintN(64,t));if(Kt(t)){if(\"string\"===e)return e=Math.trunc(Number(t)),Number.isSafeInteger(e)&&e>=0?t=String(e):(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),qt(t)||(Vt(t),t=Gt(Pt,Ot))),t;if(\"number\"===e)return(t=Math.trunc(t))>=0&&Number.isSafeInteger(t)?t:function(t){if(t<0){Nt(t);const e=Gt(Pt,Ot);return t=Number(e),Number.isSafeInteger(t)?t:e}return qt(String(t))?t:(Nt(t),Dt(Pt,Ot))}(t)}}function ee(t){if(\"string\"!=typeof t)throw Error();return t}function ne(t){if(null!=t&&\"string\"!=typeof t)throw Error();return t}function re(t){return null==t||\"string\"==typeof t?t:void 0}function ie(t,e,n,r){if(null!=t&&\"object\"==typeof t&&t.Y===at)return t;if(!Array.isArray(t))return n?2&r?(t=e[Y])?e=t:(rt((t=new e).u),e=e[Y]=t):e=new e:e=void 0,e;let i=n=tt(t);return 0===i&&(i|=32&r),i|=2&r,i!==n&&nt(t,i),new e(t)}function se(t,e,n){if(e)t:{if(!Kt(e=t))throw V(\"int64\");switch(typeof e){case\"string\":e=Zt(e);break t;case\"bigint\":if(t=e=BigInt.asIntN(64,e),Tt(t)){if(!/^\\s*(?:-?[1-9]\\d*|0)?\\s*$/.test(t))throw Error(String(t))}else if(wt(t)&&!Number.isSafeInteger(t))throw Error(String(t));e=bt?BigInt(e):At(e)?e?\"1\":\"0\":Tt(e)?e.trim()||\"0\":String(e);break t;default:e=Jt(e)}}else e=Qt(t);return\"string\"==typeof(n=null==(t=e)?n?0:void 0:t)&&(e=+n,Number.isSafeInteger(e))?e:n}function oe(t){if(void 0===ce&&(ce=\"function\"==typeof Proxy?ye(Proxy):null),!ce||!me())return t;let e=ae?.get(t);return e||(Math.random()>.01?t:(function(t){if(void 0===le){const t=new ce([],{});le=1===Array.prototype.concat.call([],t).length}le&&\"function\"==typeof Symbol&&Symbol.isConcatSpreadable&&(t[Symbol.isConcatSpreadable]=!0)}(t),e=new ce(t,{set:(t,e,n)=>(j(),t[e]=n,!0)}),function(t,e){(ae||=new ue).set(t,e),(he||=new ue).set(e,t)}(t,e),e))}let ae,he,ce,ue,le,fe,de,pe,ge;function me(){return void 0===ue&&(ue=\"function\"==typeof WeakMap?ye(WeakMap):null),ue}function ye(t){try{return-1!==t.toString().indexOf(\"[native code]\")?t:null}catch{return null}}function _e(t,e,n){if(f&&me()){if(fe?.get(e)?.get(t)){if(n)return}else if(Math.random()>.01)return;var r=t.length;n={length:r};for(var i=0;i<Math.min(r,10);i++){if(r<=10)var s=i;else{s=r/10;const t=Math.floor(i*s);s=t+Math.floor(Math.random()*(Math.floor((i+1)*s)-t))}n[s]=t[s]}Ee(t,n)?((i=(r=fe||=new ue).get(e))||(i=new ue,r.set(e,i)),i.set(t,n)):(j(),Te(t,e))}}function ve(t,e){const n=fe?.get(e)?.get(t);n&&!Ee(t,n)&&(j(),Te(t,e))}function Ee(t,e){if(t.length!==e.length)return!1;for(const i in e){var n,r=Number(i);if((n=Number.isInteger(r))&&(n=t[r],r=e[r],n=!(Number.isNaN(n)?Number.isNaN(r):n===r)),n)return!1}return!0}function we(t){if(t&&fe?.has(t)){var e=t.u;if(e)for(let n=0;n<e.length;n++){const r=e[n];if(n===e.length-1&&ut(r))for(const e in r){const n=r[e];Array.isArray(n)&&ve(n,t)}else Array.isArray(r)&&ve(r,t)}}}function Te(t,e){fe?.get(e)?.delete(t)}function Ae(t,e){return t=be(t,e[0],e[1]),Z(t,16384),t}function be(t,e,n){if(null==t&&(t=de),de=void 0,null==t){var r=96;n?(t=[n],r|=512):t=[],e&&(r=-33521665&r|(1023&e)<<15)}else{if(!Array.isArray(t))throw Error(\"narr\");if(2048&(r=tt(t)))throw Error(\"farr\");if(64&r)return t;if(r|=64,n&&(r|=512,n!==t[0]))throw Error(\"mid\");t:{const i=(n=t).length;if(i){const t=i-1;if(ut(n[t])){if((e=t-(+!!(512&(r|=256))-1))>=1024)throw Error(\"pvtlmt\");r=-33521665&r|(1023&e)<<15;break t}}if(e){if((e=Math.max(e,i-(+!!(512&r)-1)))>1024)throw Error(\"spvt\");r=-33521665&r|(1023&e)<<15}}}return nt(t,r),t}const ke={};let Se=function(){try{return v(new class extends Map{constructor(){super()}}),!1}catch{return!0}}();class xe{constructor(){this.g=new Map}get(t){return this.g.get(t)}set(t,e){return this.g.set(t,e),this.size=this.g.size,this}delete(t){return t=this.g.delete(t),this.size=this.g.size,t}clear(){this.g.clear(),this.size=this.g.size}has(t){return this.g.has(t)}entries(){return this.g.entries()}keys(){return this.g.keys()}values(){return this.g.values()}forEach(t,e){return this.g.forEach(t,e)}[Symbol.iterator](){return this.entries()}}const Le=Se?(Object.setPrototypeOf(xe.prototype,Map.prototype),Object.defineProperties(xe.prototype,{size:{value:0,configurable:!0,enumerable:!0,writable:!0}}),xe):class extends Map{constructor(){super()}};function Re(t){return t}function Fe(t){if(2&t.M)throw Error(\"Cannot mutate an immutable Map\")}var Me=class extends Le{constructor(t,e,n=Re,r=Re){super();let i=tt(t);i|=64,nt(t,i),this.M=i,this.U=e,this.T=n,this.aa=this.U?Ie:r;for(let s=0;s<t.length;s++){const o=t[s],a=n(o[0],!1,!0);let h=o[1];e?void 0===h&&(h=null):h=r(o[1],!1,!0,void 0,void 0,i),super.set(a,h)}}pa(t=Pe){if(0!==this.size)return this.Z(t)}Z(t=Pe){const e=[],n=super.entries();for(var r;!(r=n.next()).done;)(r=r.value)[0]=t(r[0]),r[1]=t(r[1]),e.push(r);return e}clear(){Fe(this),super.clear()}delete(t){return Fe(this),super.delete(this.T(t,!0,!1))}entries(){var t=this.oa();return new gt(t,Oe,this)}keys(){return this.Ka()}values(){var t=this.oa();return new gt(t,Me.prototype.get,this)}forEach(t,e){super.forEach(((n,r)=>{t.call(e,this.get(r),r,this)}))}set(t,e){return Fe(this),null==(t=this.T(t,!0,!1))?this:null==e?(super.delete(t),this):super.set(t,this.aa(e,!0,!0,this.U,!1,this.M))}Qa(t){const e=this.T(t[0],!1,!0);t=t[1],t=this.U?void 0===t?null:t:this.aa(t,!1,!0,void 0,!1,this.M),super.set(e,t)}has(t){return super.has(this.T(t,!1,!1))}get(t){t=this.T(t,!1,!1);const e=super.get(t);if(void 0!==e){var n=this.U;return n?((n=this.aa(e,!1,!0,n,this.va,this.M))!==e&&super.set(t,n),n):e}}oa(){return Array.from(super.keys())}Ka(){return super.keys()}[Symbol.iterator](){return this.entries()}};function Ie(t,e,n,r,i,s){return t=ie(t,r,n,s),i&&(t=He(t)),t}function Pe(t){return t}function Oe(t){return[t,this.get(t)]}let Ce;function Ne(){return Ce||=new Me(rt([]),void 0,void 0,void 0,ke)}function Ue(t,e,n,r,i){if(null!=t){if(Array.isArray(t))t=ft(t)?void 0:i&&2&tt(t)?t:De(t,e,n,void 0!==r,i);else if(ut(t)){const s={};for(let o in t)s[o]=Ue(t[o],e,n,r,i);t=s}else t=e(t,r);return t}}function De(t,e,n,r,i){const s=r||n?tt(t):0;r=r?!!(32&s):void 0;const o=H(t);for(let t=0;t<o.length;t++)o[t]=Ue(o[t],e,n,r,i);return n&&(yt(o,t),n(s,o)),o}function Be(t){return Ue(t,Ge,void 0,void 0,!1)}function Ge(t){return t.Y===at?t.toJSON():t instanceof Me?t.pa(Be):function(t){switch(typeof t){case\"number\":return isFinite(t)?t:String(t);case\"bigint\":return kt(t)?Number(t):String(t);case\"boolean\":return t?1:0;case\"object\":if(t)if(Array.isArray(t)){if(ft(t))return}else{if(I(t))return x(t);if(t instanceof D){const e=t.ba;return null==e?\"\":\"string\"==typeof e?e:t.ba=x(e)}if(t instanceof Me)return t.pa()}}return t}(t)}function je(t,e,n=st){if(null!=t){if(k&&t instanceof Uint8Array)return e?t:new Uint8Array(t);if(Array.isArray(t)){var r=tt(t);return 2&r?t:(e&&=0===r||!!(32&r)&&!(64&r||!(16&r)),e?(nt(t,-12293&(34|r)),t):De(t,je,4&r?st:n,!0,!0))}return t.Y===at?(n=t.u,t=2&(r=et(n))?t:Ve(t,n,r,!0)):t instanceof Me&&!(2&t.M)&&(n=rt(t.Z(je)),t=new Me(n,t.U,t.T,t.aa)),t}}function Ve(t,e,n,r){return we(t),t=t.constructor,de=e=Xe(e,n,r),e=new t(e),de=void 0,e}function Xe(t,e,n){const r=n||2&e?st:it,i=!!(32&e);return t=function(t,e,n){const r=H(t);var i=r.length;const s=256&e?r[i-1]:void 0;for(i+=s?-1:0,e=512&e?1:0;e<i;e++)r[e]=n(r[e]);if(s){e=r[e]={};for(const t in s)e[t]=n(s[t])}return yt(r,t),r}(t,e,(t=>je(t,i,r))),Z(t,32|(n?2:0)),t}function He(t){const e=t.u,n=et(e);return 2&n?Ve(t,e,n,!1):t}function We(t,e,n,r){return!(4&e)||null!=n&&(!r&&0===n&&(4096&e||8192&e)&&(t.constructor[J]=1+(0|t.constructor[J]))<5&&j(),0!==n&&!(n&e))}function ze(t,e){return Ye(t=t.u,et(t),e)}function Ke(t,e,n,r){if(!((e=r+(+!!(512&e)-1))<0||e>=t.length||e>=n))return t[e]}function Ye(t,e,n,r){if(-1===n)return null;const i=e>>15&1023||536870912;if(!(n>=i)){var s=t.length;return r&&256&e&&null!=(r=t[s-1][n])?(Ke(t,e,i,n)&&null!=$&&((e=(t=G??={})[$]||0)>=4||(t[$]=e+1,j())),r):Ke(t,e,i,n)}return 256&e?t[t.length-1][n]:void 0}function $e(t,e,n){const r=t.u;let i=et(r);return pt(i),qe(r,i,e,n),t}function qe(t,e,n,r){const i=e>>15&1023||536870912;if(n>=i){let s,o=e;if(256&e)s=t[t.length-1];else{if(null==r)return o;s=t[i+(+!!(512&e)-1)]={},o|=256}return s[n]=r,n<i&&(t[n+(+!!(512&e)-1)]=void 0),o!==e&&nt(t,o),o}return t[n+(+!!(512&e)-1)]=r,256&e&&(n in(t=t[t.length-1])&&delete t[n]),e}function Je(t,e,n,r,i){var s=2&e;i=Ye(t,e,n,i),Array.isArray(i)||(i=ot);const o=!(2&r);r=!(1&r);const a=!!(32&e);let h=tt(i);return 0!==h||!a||s||o?1&h||(h|=1,nt(i,h)):(h|=33,nt(i,h)),s?(t=!1,2&h||(rt(i),t=!!(4&h)),(r||t)&&Object.freeze(i)):(s=!!(2&h)||!!(2048&h),r&&s?(i=H(i),s=1,a&&!o&&(s|=32),nt(i,s),qe(t,e,n,i)):o&&32&h&&!s&&Q(i,32)),i}function Ze(t,e){t=t.u;let n=et(t);const r=Ye(t,n,e),i=Ht(r);return null!=i&&i!==r&&qe(t,n,e,i),i}function Qe(t){t=t.u;let e=et(t);const n=Ye(t,e,1),r=lt(n,!0,!!(34&e));return null!=r&&r!==n&&qe(t,e,1,r),r}function tn(){return void 0===_t?2:5}function en(t,e,n,r,i,s){const o=t.u;let a=et(o);r=2&a?1:r,s=!!s,i=nn(o,a,e,i);var h=tt(i),c=i;if(ve(c,t),2!==r&&1!==r||Te(c,t),We(t,h,void 0,s)){4&h&&(i=H(i),h=vn(h,a),a=qe(o,a,e,i));let t=c=0;for(;c<i.length;c++){const e=n(i[c]);null!=e&&(i[t++]=e)}t<c&&(i.length=t),h=-4097&(20|(h=rn(h,a))),nt(i,h&=-8193),2&h&&Object.freeze(i)}let u;return 1===r||4===r&&32&h?sn(h)||(t=h,(h|=2)!==t&&nt(i,h),Object.freeze(i)):(n=5===r&&(!!(32&h)||sn(h)||!!ae?.get(i)),(2===r||n)&&sn(h)&&(i=H(i),h=En(h=vn(h,a),a,s),nt(i,h),a=qe(o,a,e,i)),sn(h)||(e=h,(h=En(h,a,s))!==e&&nt(i,h)),n?(u=oe(i),_e(i,t,!0)):2!==r||s||ae?.delete(i)),u||i}function nn(t,e,n,r){return t=Ye(t,e,n,r),Array.isArray(t)?t:ot}function rn(t,e){return 0===t&&(t=vn(t,e)),1|t}function sn(t){return!!(2&t)&&!!(4&t)||!!(2048&t)}function on(t){t=H(t);for(let e=0;e<t.length;e++){const n=t[e]=H(t[e]);Array.isArray(n[1])&&(n[1]=rt(n[1]))}return t}function an(t,e,n,r){t=t.u;let i=et(t);pt(i),qe(t,i,e,(\"0\"===r?0===Number(n):n===r)?void 0:n)}function hn(t,e){var n=_s;return ln(cn(t=t.u),t,et(t),n)===e?e:-1}function cn(t){if(W)return t[q]??(t[q]=new Map);if(q in t)return t[q];const e=new Map;return Object.defineProperty(t,q,{value:e}),e}function un(t,e,n,r){const i=cn(t),s=ln(i,t,e,n);return s!==r&&(s&&(e=qe(t,e,s)),i.set(n,r)),e}function ln(t,e,n,r){let i=t.get(r);if(null!=i)return i;i=0;for(let t=0;t<r.length;t++){const s=r[t];null!=Ye(e,n,s)&&(0!==i&&(n=qe(e,n,i)),i=s)}return t.set(r,i),i}function fn(t,e,n,r){let i,s=et(t);if(null!=(r=Ye(t,s,n,r))&&r.Y===at)return(e=He(r))!==r&&qe(t,s,n,e),e.u;if(Array.isArray(r)){const t=tt(r);i=2&t?Xe(r,t,!1):r,i=Ae(i,e)}else i=Ae(void 0,e);return i!==r&&qe(t,s,n,i),i}function dn(t,e,n,r){t=t.u;let i=et(t);return(e=ie(r=Ye(t,i,n,r),e,!1,i))!==r&&null!=e&&qe(t,i,n,e),e}function pn(t,e,n,r=!1){if(null==(e=dn(t,e,n,r)))return e;if(t=t.u,!(2&(r=et(t)))){const i=He(e);i!==e&&qe(t,r,n,e=i)}return e}function gn(t,e,n,r,i,s,o){const a=t.u;var h=!!(2&e);i=h?1:i,s=!!s,o&&=!h,h=nn(a,e,r);var c=tt(h),u=h;if(ve(u,t),2!==i&&1!==i||Te(u,t),!(u=!!(4&c))){var l=h,f=e;const t=!!(2&(c=rn(c,e)));t&&(f|=2);let r=!t,i=!0,s=0,o=0;for(;s<l.length;s++){const e=ie(l[s],n,!1,f);if(e instanceof n){if(!t){const t=!!(2&tt(e.u));r&&=!t,i&&=t}l[o++]=e}}o<s&&(l.length=o),c|=4,c=i?16|c:-17&c,nt(l,c=r?8|c:-9&c),t&&Object.freeze(l)}if(o&&!(8&c||!h.length&&(1===i||4===i&&32&c))){for(sn(c)?(h=H(h),c=vn(c,e),e=qe(a,e,r,h)):Te(h,t),n=h,o=c,l=0;l<n.length;l++)(c=n[l])!==(f=He(c))&&(n[l]=f);o|=8,o=n.length?-17&o:16|o,nt(n,o),c=o}let d;return 1===i||4===i&&32&c?sn(c)||(t=c,(c|=!h.length||16&c&&(!u||32&c)?2:2048)!==t&&nt(h,c),Object.freeze(h)):(u=5===i&&(!!(32&c)||sn(c)||!!ae?.get(h)),(2===i||u)&&sn(c)&&(h=H(h),c=En(c=vn(c,e),e,s),nt(h,c),e=qe(a,e,r,h)),sn(c)||(r=c,(c=En(c,e,s))!==r&&nt(h,c)),u?(d=oe(h),_e(h,t,!0)):2!==i||s||ae?.delete(h)),d||h}function mn(t,e,n){const r=et(t.u);return gn(t,r,e,n,tn(),!1,!(2&r))}function yn(t,e,n,r){return null==r&&(r=void 0),$e(t,n,r)}function _n(t,e,n,r){null==r&&(r=void 0);t:{t=t.u;let i=et(t);if(pt(i),null==r){const r=cn(t);if(ln(r,t,i,n)!==e)break t;r.set(n,0)}else i=un(t,i,n,e);qe(t,i,e,r)}}function vn(t,e){return-2049&(t=32|(2&e?2|t:-3&t))}function En(t,e,n){return 32&e&&n||(t&=-33),t}function wn(t,e,n,r){const i=et(t.u);pt(i),t=gn(t,i,n,e,2,!0),n=null!=r?r:new n,t.push(n),2&tt(n.u)?Q(t,8):Q(t,16)}function Tn(t,e){return t??e}function An(t,e){return Yt(ze(t,e))}function bn(t,e){return Tn(Ze(t,e),0)}function kn(t,e){return Tn(re(ze(t,e)),\"\")}function Sn(t,e,n){if(null!=n&&\"boolean\"!=typeof n)throw t=typeof n,Error(`Expected boolean but got ${\"object\"!=t?t:n?Array.isArray(n)?\"array\":t:\"null\"}: ${n}`);$e(t,e,n)}function xn(t,e,n){if(null!=n){if(\"number\"!=typeof n)throw V(\"int32\");if(!Number.isFinite(n))throw V(\"int32\");n|=0}$e(t,e,n)}function Ln(t,e,n){if(null!=n&&\"number\"!=typeof n)throw Error(`Value of float/double field must be a number, found ${typeof n}: ${n}`);$e(t,e,n)}function Rn(t,e,n){{const a=t.u;let h=et(a);if(pt(h),null==n)qe(a,h,e);else{n=he?.get(n)||n;var r,i=tt(n),s=i,o=!!(2&i)||Object.isFrozen(n);if((r=!o)&&(r=void 0===vt||!1),We(t,i)){i=21,o&&(n=H(n),s=0,i=En(i=vn(i,h),h,!0));for(let t=0;t<n.length;t++)n[t]=ee(n[t])}r?(n=H(n),s=0,i=En(i=vn(i,h),h,!0)):o||_e(n,t),i!==s&&nt(n,i),qe(a,h,e,n)}}}function Fn(t,e,n){pt(et(t.u)),en(t,e,re,2,void 0,!0).push(ee(n))}function Mn(t,e){return Error(`Invalid wire type: ${t} (at position ${e})`)}function In(){return Error(\"Failed to read varint, encoding is invalid.\")}function Pn(t,e){return Error(`Tried to read past the end of the data ${e} > ${t}`)}function On(t){if(\"string\"==typeof t)return{buffer:M(t),O:!1};if(Array.isArray(t))return{buffer:new Uint8Array(t),O:!1};if(t.constructor===Uint8Array)return{buffer:t,O:!1};if(t.constructor===ArrayBuffer)return{buffer:new Uint8Array(t),O:!1};if(t.constructor===D)return{buffer:U(t)||new Uint8Array(0),O:!0};if(t instanceof Uint8Array)return{buffer:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),O:!1};throw Error(\"Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers\")}function Cn(t,e){let n,r=0,i=0,s=0;const o=t.h;let a=t.g;do{n=o[a++],r|=(127&n)<<s,s+=7}while(s<32&&128&n);for(s>32&&(i|=(127&n)>>4),s=3;s<32&&128&n;s+=7)n=o[a++],i|=(127&n)<<s;if(Xn(t,a),n<128)return e(r>>>0,i>>>0);throw In()}function Nn(t){let e=0,n=t.g;const r=n+10,i=t.h;for(;n<r;){const r=i[n++];if(e|=r,0==(128&r))return Xn(t,n),!!(127&e)}throw In()}function Un(t){const e=t.h;let n=t.g,r=e[n++],i=127&r;if(128&r&&(r=e[n++],i|=(127&r)<<7,128&r&&(r=e[n++],i|=(127&r)<<14,128&r&&(r=e[n++],i|=(127&r)<<21,128&r&&(r=e[n++],i|=r<<28,128&r&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++])))))throw In();return Xn(t,n),i}function Dn(t){return Un(t)>>>0}function Bn(t){var e=t.h;const n=t.g,r=e[n],i=e[n+1],s=e[n+2];return e=e[n+3],Xn(t,t.g+4),(r<<0|i<<8|s<<16|e<<24)>>>0}function Gn(t){var e=Bn(t);t=2*(e>>31)+1;const n=e>>>23&255;return e&=8388607,255==n?e?NaN:t*(1/0):0==n?1401298464324817e-60*t*e:t*Math.pow(2,n-150)*(e+8388608)}function jn(t){return Un(t)}function Vn(t,e,{ea:n=!1}={}){t.ea=n,e&&(e=On(e),t.h=e.buffer,t.m=e.O,t.j=0,t.l=t.h.length,t.g=t.j)}function Xn(t,e){if(t.g=e,e>t.l)throw Pn(t.l,e)}function Hn(t,e){if(e<0)throw Error(`Tried to read a negative byte length: ${e}`);const n=t.g,r=n+e;if(r>t.l)throw Pn(e,t.l-n);return t.g=r,n}function Wn(t,e){if(0==e)return N();var n=Hn(t,e);return t.ea&&t.m?n=t.h.subarray(n,n+e):(t=t.h,n=n===(e=n+e)?new Uint8Array(0):Mt?t.slice(n,e):new Uint8Array(t.subarray(n,e))),0==n.length?N():new D(n,P)}Me.prototype.toJSON=void 0,Me.prototype.La=ht;var zn=[];function Kn(t){var e=t.g;if(e.g==e.l)return!1;t.l=t.g.g;var n=Dn(t.g);if(e=n>>>3,!((n&=7)>=0&&n<=5))throw Mn(n,t.l);if(e<1)throw Error(`Invalid field number: ${e} (at position ${t.l})`);return t.m=e,t.h=n,!0}function Yn(t){switch(t.h){case 0:0!=t.h?Yn(t):Nn(t.g);break;case 1:Xn(t=t.g,t.g+8);break;case 2:if(2!=t.h)Yn(t);else{var e=Dn(t.g);Xn(t=t.g,t.g+e)}break;case 5:Xn(t=t.g,t.g+4);break;case 3:for(e=t.m;;){if(!Kn(t))throw Error(\"Unmatched start-group tag: stream EOF\");if(4==t.h){if(t.m!=e)throw Error(\"Unmatched end-group tag\");break}Yn(t)}break;default:throw Mn(t.h,t.l)}}function $n(t,e,n){const r=t.g.l,i=Dn(t.g),s=t.g.g+i;let o=s-r;if(o<=0&&(t.g.l=s,n(e,t,void 0,void 0,void 0),o=s-t.g.g),o)throw Error(`Message parsing ended unexpectedly. Expected to read ${i} bytes, instead read ${i-o} bytes, either the data ended unexpectedly or the message misreported its own length`);return t.g.g=s,t.g.l=r,e}function qn(t){var e=Dn(t.g),a=Hn(t=t.g,e);if(t=t.h,o){var h,c=t;(h=s)||(h=s=new TextDecoder(\"utf-8\",{fatal:!0})),e=a+e,c=0===a&&e===c.length?c:c.subarray(a,e);try{var u=h.decode(c)}catch(t){if(void 0===i){try{h.decode(new Uint8Array([128]))}catch(t){}try{h.decode(new Uint8Array([97])),i=!0}catch(t){i=!1}}throw!i&&(s=void 0),t}}else{e=(u=a)+e,a=[];let i,s=null;for(;u<e;){var l=t[u++];l<128?a.push(l):l<224?u>=e?n():(i=t[u++],l<194||128!=(192&i)?(u--,n()):a.push((31&l)<<6|63&i)):l<240?u>=e-1?n():(i=t[u++],128!=(192&i)||224===l&&i<160||237===l&&i>=160||128!=(192&(h=t[u++]))?(u--,n()):a.push((15&l)<<12|(63&i)<<6|63&h)):l<=244?u>=e-2?n():(i=t[u++],128!=(192&i)||i-144+(l<<28)>>30!=0||128!=(192&(h=t[u++]))||128!=(192&(c=t[u++]))?(u--,n()):(l=(7&l)<<18|(63&i)<<12|(63&h)<<6|63&c,l-=65536,a.push(55296+(l>>10&1023),56320+(1023&l)))):n(),a.length>=8192&&(s=r(s,a),a.length=0)}u=r(s,a)}return u}function Jn(t){const e=Dn(t.g);return Wn(t.g,e)}function Zn(t,e,n){var r=Dn(t.g);for(r=t.g.g+r;t.g.g<r;)n.push(e(t.g))}var Qn=[];let tr;function er(t,e,n){e.g?e.m(t,e.g,e.h,n,!0):e.m(t,e.h,n,!0)}var nr=class{constructor(t,e){this.u=be(t,e)}toJSON(){return rr(this)}l(){var t=vo;return t.g?t.l(this,t.g,t.h,!0):t.l(this,t.h,t.defaultValue,!0)}clone(){const t=this.u;return Ve(this,t,et(t),!1)}O(){return!!(2&tt(this.u))}};function rr(t){we(t),t=tr?t.u:De(t.u,Ge,void 0,void 0,!1);{var e=!tr;let c=t.length;if(c){var n=t[c-1],r=ut(n);r?c--:n=void 0;var i=t;if(r){t:{var s,o=n,a=!1;if(o)for(let t in o)isNaN(+t)?(s??={})[t]=o[t]:(r=o[t],Array.isArray(r)&&(ft(r)||ct(r)&&0===r.size)&&(r=null),null==r&&(a=!0),null!=r&&((s??={})[t]=r));if(a||(s=o),s)for(let t in s){a=s;break t}a=null}o=null==a?null!=n:a!==n}for(;c>0&&(null==(s=i[c-1])||ft(s)||ct(s)&&0===s.size);c--)var h=!0;(i!==t||o||h)&&(e?(h||o||a)&&(i.length=c):i=Array.prototype.slice.call(i,0,c),a&&i.push(a)),h=i}else h=t}return h}function ir(t){return t?/^\\d+$/.test(t)?(Vt(t),new sr(Pt,Ot)):null:or||=new sr(0,0)}nr.prototype.Y=at,nr.prototype.toString=function(){try{return tr=!0,rr(this).toString()}finally{tr=!1}};var sr=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}};let or;function ar(t){return t?/^-?\\d+$/.test(t)?(Vt(t),new hr(Pt,Ot)):null:cr||=new hr(0,0)}var hr=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}};let cr;function ur(t,e,n){for(;n>0||e>127;)t.g.push(127&e|128),e=(e>>>7|n<<25)>>>0,n>>>=7;t.g.push(e)}function lr(t,e){for(;e>127;)t.g.push(127&e|128),e>>>=7;t.g.push(e)}function fr(t,e){if(e>=0)lr(t,e);else{for(let n=0;n<9;n++)t.g.push(127&e|128),e>>=7;t.g.push(1)}}function dr(t,e){t.g.push(e>>>0&255),t.g.push(e>>>8&255),t.g.push(e>>>16&255),t.g.push(e>>>24&255)}function pr(t,e){0!==e.length&&(t.l.push(e),t.h+=e.length)}function gr(t,e,n){lr(t.g,8*e+n)}function mr(t,e){return gr(t,e,2),e=t.g.end(),pr(t,e),e.push(t.h),e}function yr(t,e){var n=e.pop();for(n=t.h+t.g.length()-n;n>127;)e.push(127&n|128),n>>>=7,t.h++;e.push(n),t.h++}function _r(t,e,n){gr(t,e,2),lr(t.g,n.length),pr(t,t.g.end()),pr(t,n)}function vr(t,e,n,r){null!=n&&(e=mr(t,e),r(n,t),yr(t,e))}class Er{constructor(t,e,n){this.g=t,this.h=e,this.qa=n}}function wr(t){return Array.isArray(t)?t[0]instanceof Er?t:[gi,t]:[t,void 0]}function Tr(t,e){if(Array.isArray(e)){var n=tt(e);if(4&n)return e;for(var r=0,i=0;r<e.length;r++){const n=t(e[r]);null!=n&&(e[i++]=n)}return i<r&&(e.length=i),nt(e,-12289&(5|n)),2&n&&Object.freeze(e),e}}const Ar=Symbol();function br(t){let e=t[Ar];if(!e){const n=Ur(t),r=n.h;e=r?(t,e)=>r(t,e,n):(t,e)=>{for(;Kn(e)&&4!=e.h;){var r=e.m;let o=n[r];const a=!o;let h=!1;if(!o){var i=n.X;if(i){var s=i[r];s&&(h=i.P?.[r],(!d||h)&&(i=kr(s))&&(o=n[r]=i))}}o&&o(e,t,r)||(r=(i=e).l,Yn(i),i.ja?i=void 0:(s=i.g.g-r,i.g.g=r,i=Wn(i.g,s)),r=t,i&&(mt||=Symbol(),(s=r[mt])?s.push(i):r[mt]=[i])),a&&o&&!h&&Wr++<5&&j()}},t[Ar]=e}return e}function kr(t){const e=(t=wr(t))[0].g;if(t=t[1]){const n=br(t),r=Ur(t).g;return(t,i,s)=>e(t,i,s,r,n)}return e}function Sr(t,e,n){t[e]=n}function xr(t,e,n,r){var i=Sr;e.g=function(t){switch(typeof t){case\"boolean\":return pe||=[0,void 0,!0];case\"number\":return t>0?void 0:0===t?ge||=[0,void 0]:[-t,void 0];case\"string\":return[0,t];case\"object\":return t}}(t[0]);let s=0;var o=t[++s];o&&o.constructor===Object&&(e.X=o,\"function\"==typeof(o=t[++s])&&(e.h=o,e.l=t[++s],o=t[++s]));const a={};for(;Array.isArray(o)&&\"number\"==typeof o[0]&&o[0]>0;){for(var h=0;h<o.length;h++)a[o[h]]=o;o=t[++s]}for(h=1;void 0!==o;){let l;\"number\"==typeof o&&(h+=o,o=t[++s]);var c=void 0;if(o instanceof Er?l=o:(l=mi,s--),l.qa){o=t[++s],c=t;var u=s;\"function\"==typeof o&&(o=o(),c[u]=o),c=o}for(u=h+1,\"number\"==typeof(o=t[++s])&&o<0&&(u-=o,o=t[++s]);h<u;h++){const t=a[h];i(e,h,c?r(l,c,t):n(l,t))}}return e}const Lr=Symbol();function Rr(t){let e=t[Lr];if(!e){const n=Pr(t);e=(t,e)=>Br(t,e,n),t[Lr]=e}return e}const Fr=Symbol();function Mr(t){return t.h}function Ir(t,e){let n,r;const i=t.h;return(t,s,o)=>i(t,s,o,r||=Pr(e).g,n||=Rr(e))}function Pr(t){let e=t[Fr];return e||(e=xr(t,t[Fr]={},Mr,Ir))}const Or=Symbol();function Cr(t,e){const n=t.g;return e?(t,r,i)=>n(t,r,i,e):n}function Nr(t,e,n){const r=t.g;let i,s;return(t,o,a)=>r(t,o,a,s||=Ur(e).g,i||=br(e),n)}function Ur(t){let e=t[Or];return e||(e=xr(t,t[Or]={},Cr,Nr))}function Dr(t,e){var n=t[e];if(n)return n;if(n=t.X){var r=n[e];if(r){var i=(r=wr(r))[0].h;if(r=r[1],n=n.P?.[e],!d||n){if(r){const e=Rr(r),s=Pr(r).g;n=(n=t.l)?n(s,e):(t,n,r)=>i(t,n,r,s,e)}else n=i;return t[e]=n}}}}function Br(t,e,n){for(var r=et(t),i=+!!(512&r)-1,s=t.length,o=512&r?1:0,a=s+(256&r?-1:0);o<a;o++){const r=t[o];if(null==r)continue;const s=o-i,a=Dr(n,s);if(!a)continue;const h=n.X;h?.[s]&&!h?.P?.[s]&&Wr++<5&&j(),a(e,r,s)}if(256&r){r=t[s-1];for(let t in r)i=+t,!Number.isNaN(i)&&(null!=(s=r[t])&&(a=Dr(n,i)))&&((o=n.X)?.[i]&&!o?.P?.[i]&&Wr++<5&&j(),a(e,s,i))}if(t=mt?t[mt]:void 0)for(pr(e,e.g.end()),n=0;n<t.length;n++)pr(e,U(t[n])||new Uint8Array(0))}function Gr(t,e){return new Er(t,e,!1)}function jr(t,e){return new Er(t,e,!1)}function Vr(t,e){return new Er(t,e,!0)}function Xr(t,e,n){qe(t,et(t),e,n)}var Hr=Vr((function(t,e,n,r,i){return 2===t.h&&(t=$n(t,Ae([void 0,void 0],r),i),pt(r=et(e)),(i=Ye(e,r,n))instanceof Me?0!=(2&i.M)?((i=i.Z()).push(t),qe(e,r,n,i)):i.Qa(t):Array.isArray(i)?(2&tt(i)&&qe(e,r,n,i=on(i)),i.push(t)):qe(e,r,n,[t]),!0)}),(function(t,e,n,r,i){if(e instanceof Me)e.forEach(((e,s)=>{vr(t,n,Ae([s,e],r),i)}));else if(Array.isArray(e))for(let s=0;s<e.length;s++){const o=e[s];Array.isArray(o)&&vr(t,n,Ae(o,r),i)}}));let Wr=0;function zr(t,e,n){if(e=function(t){if(null==t)return t;const e=typeof t;if(\"bigint\"===e)return String(BigInt.asIntN(64,t));if(Kt(t)){if(\"string\"===e)return Zt(t);if(\"number\"===e)return Jt(t)}}(e),null!=e){if(\"string\"==typeof e)ar(e);if(null!=e)switch(gr(t,n,0),typeof e){case\"number\":t=t.g,Nt(e),ur(t,Pt,Ot);break;case\"bigint\":n=BigInt.asUintN(64,e),n=new hr(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),ur(t.g,n.h,n.g);break;default:n=ar(e),ur(t.g,n.h,n.g)}}}function Kr(t,e,n){null!=(e=Yt(e))&&null!=e&&(gr(t,n,0),fr(t.g,e))}function Yr(t,e,n){null!=(e=Wt(e))&&(gr(t,n,0),t.g.g.push(e?1:0))}function $r(t,e,n){null!=(e=re(e))&&_r(t,n,c(e))}function qr(t,e,n,r,i){e instanceof nr?(we(e),e=e.u):e=Array.isArray(e)?Ae(e,r):void 0,vr(t,n,e,i)}function Jr(t,e,n){null!=(e=null==e||\"string\"==typeof e||I(e)||e instanceof D?e:void 0)&&_r(t,n,On(e).buffer)}function Zr(t,e,n){return(5===t.h||2===t.h)&&(e=Je(e,et(e),n,2,!1),2==t.h?Zn(t,Gn,e):e.push(Gn(t.g)),!0)}var Qr,ti=Gr((function(t,e,n){if(1!==t.h)return!1;var r=t.g;t=Bn(r);const i=Bn(r);r=2*(i>>31)+1;const s=i>>>20&2047;return t=4294967296*(1048575&i)+t,Xr(e,n,2047==s?t?NaN:r*(1/0):0==s?5e-324*r*t:r*Math.pow(2,s-1075)*(t+4503599627370496)),!0}),(function(t,e,n){null!=(e=Ht(e))&&(gr(t,n,1),t=t.g,(n=It||=new DataView(new ArrayBuffer(8))).setFloat64(0,+e,!0),Pt=n.getUint32(0,!0),Ot=n.getUint32(4,!0),dr(t,Pt),dr(t,Ot))})),ei=Gr((function(t,e,n){return 5===t.h&&(Xr(e,n,Gn(t.g)),!0)}),(function(t,e,n){null!=(e=Ht(e))&&(gr(t,n,5),t=t.g,Ut(e),dr(t,Pt))})),ni=jr(Zr,(function(t,e,n){if(null!=(e=Tr(Ht,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&(gr(r,i,5),r=r.g,Ut(s),dr(r,Pt))}})),ri=jr(Zr,(function(t,e,n){if(null!=(e=Tr(Ht,e))&&e.length){gr(t,n,2),lr(t.g,4*e.length);for(let r=0;r<e.length;r++)n=t.g,Ut(e[r]),dr(n,Pt)}})),ii=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,Cn(t.g,Bt)),!0)}),zr),si=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,0===(t=Cn(t.g,Bt))?void 0:t),!0)}),zr),oi=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,Cn(t.g,Dt)),!0)}),(function(t,e,n){if(null!=(e=te(e))){if(\"string\"==typeof e)ir(e);if(null!=e)switch(gr(t,n,0),typeof e){case\"number\":t=t.g,Nt(e),ur(t,Pt,Ot);break;case\"bigint\":n=BigInt.asUintN(64,e),n=new sr(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),ur(t.g,n.h,n.g);break;default:n=ir(e),ur(t.g,n.h,n.g)}}})),ai=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,Un(t.g)),!0)}),Kr),hi=jr((function(t,e,n){return(0===t.h||2===t.h)&&(e=Je(e,et(e),n,2,!1),2==t.h?Zn(t,Un,e):e.push(Un(t.g)),!0)}),(function(t,e,n){if(null!=(e=Tr(Yt,e))&&e.length){n=mr(t,n);for(let n=0;n<e.length;n++)fr(t.g,e[n]);yr(t,n)}})),ci=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,0===(t=Un(t.g))?void 0:t),!0)}),Kr),ui=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,Nn(t.g)),!0)}),Yr),li=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,!1===(t=Nn(t.g))?void 0:t),!0)}),Yr),fi=jr((function(t,e,n){if(2!==t.h)return!1;t=qn(t);const r=et(e);return pt(r),Je(e,r,n,2).push(t),!0}),(function(t,e,n){if(null!=(e=Tr(re,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&_r(r,i,c(s))}})),di=Gr((function(t,e,n){return 2===t.h&&(Xr(e,n,\"\"===(t=qn(t))?void 0:t),!0)}),$r),pi=Gr((function(t,e,n){return 2===t.h&&(Xr(e,n,qn(t)),!0)}),$r),gi=Vr((function(t,e,n,r,i){return 2===t.h&&($n(t,fn(e,r,n,!0),i),!0)}),qr),mi=Vr((function(t,e,n,r,i){return 2===t.h&&($n(t,fn(e,r,n),i),!0)}),qr);Qr=new Er((function(t,e,n,r,i){if(2!==t.h)return!1;r=Ae(void 0,r);let s=et(e);pt(s);let o=Je(e,s,n,3);return s=et(e),4&tt(o)&&(o=H(o),nt(o,-2079&(1|tt(o))),qe(e,s,n,o)),o.push(r),$n(t,r,i),!0}),(function(t,e,n,r,i){if(Array.isArray(e))for(let s=0;s<e.length;s++)qr(t,e[s],n,r,i)}),!0);var yi=Vr((function(t,e,n,r,i,s){return 2===t.h&&(un(e,et(e),s,n),$n(t,e=fn(e,r,n),i),!0)}),qr),_i=Gr((function(t,e,n){return 2===t.h&&(Xr(e,n,Jn(t)),!0)}),Jr),vi=jr((function(t,e,n){return(0===t.h||2===t.h)&&(e=Je(e,et(e),n,2,!1),2==t.h?Zn(t,Dn,e):e.push(Dn(t.g)),!0)}),(function(t,e,n){if(null!=(e=Tr($t,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&(gr(r,i,0),lr(r.g,s))}})),Ei=Gr((function(t,e,n){return 0===t.h&&(Xr(e,n,Un(t.g)),!0)}),(function(t,e,n){null!=(e=Yt(e))&&(e=parseInt(e,10),gr(t,n,0),fr(t.g,e))}));class wi{constructor(t,e){this.h=t,this.g=e,this.l=pn,this.m=yn,this.defaultValue=void 0}}function Ti(t,e){return new wi(t,e)}function Ai(t,e){return(n,r)=>{if(Qn.length){const t=Qn.pop();t.o(r),Vn(t.g,n,r),n=t}else n=new class{constructor(t,e){if(zn.length){const n=zn.pop();Vn(n,t,e),t=n}else t=new class{constructor(t,e){this.h=null,this.m=!1,this.g=this.l=this.j=0,Vn(this,t,e)}clear(){this.h=null,this.m=!1,this.g=this.l=this.j=0,this.ea=!1}}(t,e);this.g=t,this.l=this.g.g,this.h=this.m=-1,this.o(e)}o({ja:t=!1}={}){this.ja=t}}(n,r);try{const r=new t,s=r.u;br(e)(s,n);var i=r}finally{n.g.clear(),n.m=-1,n.h=-1,Qn.length<100&&Qn.push(n)}return i}}function bi(t){return function(){we(this);const e=new class{constructor(){this.l=[],this.h=0,this.g=new class{constructor(){this.g=[]}length(){return this.g.length}end(){const t=this.g;return this.g=[],t}}}};Br(this.u,e,Pr(t)),pr(e,e.g.end());const n=new Uint8Array(e.h),r=e.l,i=r.length;let s=0;for(let t=0;t<i;t++){const e=r[t];n.set(e,s),s+=e.length}return e.l=[n],n}}var ki=class extends nr{constructor(t){super(t)}},Si=[0,di,Gr((function(t,e,n){return 2===t.h&&(Xr(e,n,(t=Jn(t))===N()?void 0:t),!0)}),(function(t,e,n){if(null!=e){if(e instanceof nr){const r=e.Ta;return void(r&&(e=r(e),null!=e&&_r(t,n,On(e).buffer)))}if(Array.isArray(e))return}Jr(t,e,n)}))],xi=[0,ai,Ei,ui,-1,hi,Ei,-1],Li=class extends nr{constructor(){super()}},Ri=[0,ui,pi,ui,Ei,-1,jr((function(t,e,n){return(0===t.h||2===t.h)&&(e=Je(e,et(e),n,2,!1),2==t.h?Zn(t,jn,e):e.push(Un(t.g)),!0)}),(function(t,e,n){if(null!=(e=Tr(Yt,e))&&e.length){n=mr(t,n);for(let n=0;n<e.length;n++)fr(t.g,e[n]);yr(t,n)}})),pi,-1,[0,ui,-1],Ei,ui,-1],Fi=[0,pi,-2],Mi=class extends nr{constructor(){super()}},Ii=[0],Pi=[0,ai,ui,1,ui,-3],Oi=[0,pi,ui,-1,ai,[0,[1,2,3,4,5,6,7],yi,Ii,yi,Ri,yi,Fi,yi,Pi,yi,xi,yi,[0,pi,-2],yi,[0,pi,Ei]],[0,pi],ui,[0,[1,3],[2,4],yi,[0,hi],-1,yi,[0,fi],-1,Qr,[0,pi,-1]],pi],Ci=class extends nr{constructor(t){super(t,2)}},Ni={},Ui=Ni.P={};Ni[336783863]=Oi,Ui[336783863]=1;var Di=[0,si,-1,li,-3,si,hi,di,ci,si,-1,li,ci,li,-2,di];function Bi(t,e){an(t,2,ne(e),\"\")}function Gi(t,e){Fn(t,3,e)}function ji(t,e){Fn(t,4,e)}var Vi=class extends nr{constructor(t){super(t,500)}o(t){return yn(this,0,7,t)}},Xi=[-1,{P:{}}],Hi=[0,pi,1,Xi],Wi=[0,pi,fi,Xi];function zi(t,e){wn(t,1,Vi,e)}function Ki(t,e){Fn(t,10,e)}function Yi(t,e){Fn(t,15,e)}var $i=class extends nr{constructor(t){super(t,500)}o(t){return yn(this,0,1001,t)}},qi=[-500,Qr,[-500,di,-1,fi,-3,[-2,Ni,ui],Qr,Si,ci,-1,Hi,Wi,Qr,[0,di,li],di,Di,ci,fi,987,fi],4,Qr,[-500,pi,-1,[-1,{P:{}}],998,pi],Qr,[-500,pi,fi,-1,[-2,{P:{}},ui],997,fi,-1],ci,Qr,[-500,pi,fi,Xi,998,fi],fi,ci,Hi,Wi,Qr,[0,di,-1,Xi],fi,-2,Di,di,-1,li,979,Xi,Qr,Si];$i.prototype.g=bi(qi);var Ji=Ai($i,qi),Zi=class extends nr{constructor(t){super(t)}},Qi=class extends nr{constructor(t){super(t)}g(){return mn(this,Zi,1)}},ts=[0,Qr,[0,ai,ei,pi,-1]],es=Ai(Qi,ts),ns=class extends nr{constructor(t){super(t)}},rs=class extends nr{constructor(t){super(t)}},is=class extends nr{constructor(t){super(t)}h(){return pn(this,ns,2)}g(){return mn(this,rs,5)}},ss=Ai(class extends nr{constructor(t){super(t)}},[0,fi,hi,ri,[0,Ei,[0,ai,-3],[0,ei,-3],[0,ai,-1,[0,Qr,[0,ai,-2]]],Qr,[0,ei,-1,pi,ei]],pi,-1,ii,Qr,[0,ai,ei],fi,ii]),os=class extends nr{constructor(t){super(t)}},as=Ai(class extends nr{constructor(t){super(t)}},[0,Qr,[0,ei,-4]]),hs=class extends nr{constructor(t){super(t)}},cs=Ai(class extends nr{constructor(t){super(t)}},[0,Qr,[0,ei,-4]]),us=class extends nr{constructor(t){super(t)}},ls=[0,ai,-1,ri,Ei],fs=class extends nr{constructor(){super()}};fs.prototype.g=bi([0,ei,-4,ii]);var ds=class extends nr{constructor(t){super(t)}},ps=Ai(class extends nr{constructor(t){super(t)}},[0,Qr,[0,1,ai,pi,ts],ii]),gs=class extends nr{constructor(t){super(t)}},ms=class extends nr{constructor(t){super(t)}ra(){const t=Qe(this);return null==t?N():t}},ys=class extends nr{constructor(t){super(t)}},_s=[1,2],vs=Ai(class extends nr{constructor(t){super(t)}},[0,Qr,[0,_s,yi,[0,ri],yi,[0,_i],ai,pi],ii]),Es=class extends nr{constructor(t){super(t)}},ws=[0,pi,ai,ei,fi,-1],Ts=class extends nr{constructor(t){super(t)}},As=[0,ui,-1],bs=class extends nr{constructor(t){super(t)}},ks=[1,2,3,4,5],Ss=class extends nr{constructor(t){super(t)}g(){return null!=Qe(this)}h(){return null!=re(ze(this,2))}},xs=class extends nr{constructor(t){super(t)}g(){return Wt(ze(this,2))??!1}},Ls=[0,_i,pi,[0,ai,ii,-1],[0,oi,ii]],Rs=[0,Ls,ui,[0,ks,yi,Pi,yi,Ri,yi,xi,yi,Ii,yi,Fi],Ei],Fs=class extends nr{constructor(t){super(t)}},Ms=[0,Rs,ei,-1,ai],Is=Ti(502141897,Fs);Ni[502141897]=Ms,Ui[502141897]=1;var Ps=[0,Ls];Ni[512499200]=Ps;var Os=[0,Ps];Ni[515723506]=Os;var Cs=Ai(class extends nr{constructor(t){super(t)}},[0,[0,Ei,-1,ni,vi],ls]),Ns=[0,Rs];Ni[508981768]=Ns;var Us=class extends nr{constructor(t){super(t)}},Ds=class extends nr{constructor(t){super(t)}},Bs=[0,Rs,ei,Ns,ui],Gs=[0,Rs,Ms,Bs,ei,Os];Ni[508968149]=Bs;var js=Ti(508968150,Ds);Ni[508968150]=Gs,Ui[508968150]=1,Ui[508968149]=1;var Vs=class extends nr{constructor(t){super(t)}},Xs=Ti(513916220,Vs);Ni[513916220]=[0,Rs,Gs,ai],Ui[513916220]=1;var Hs=class extends nr{constructor(t){super(t)}h(){return pn(this,Es,2)}g(){$e(this,2)}},Ws=[0,Rs,ws];Ni[478825465]=Ws,Ui[478825465]=1;var zs=[0,Rs];Ni[478825422]=zs;var Ks=class extends nr{constructor(t){super(t)}},Ys=class extends nr{constructor(t){super(t)}},$s=class extends nr{constructor(t){super(t)}},qs=class extends nr{constructor(t){super(t)}},Js=class extends nr{constructor(t){super(t)}},Zs=[0,Rs,zs,Ws,-1],Qs=[0,Rs,ei,ai],to=[0,Rs,ei],eo=[0,Rs,Qs,to,ei],no=[0,Rs,eo,Zs];Ni[463370452]=Zs,Ni[464864288]=Qs,Ni[474472470]=to;var ro=Ti(462713202,qs);Ni[462713202]=eo;var io=Ti(479097054,Js);Ni[479097054]=no,Ui[479097054]=1,Ui[463370452]=1,Ui[464864288]=1,Ui[462713202]=1,Ui[474472470]=1;var so=class extends nr{constructor(t){super(t)}},oo=class extends nr{constructor(t){super(t)}},ao=class extends nr{constructor(t){super(t)}},ho=class extends nr{constructor(){super()}},co=[0,Rs,ei,-1,ai],uo=[0,Rs,ei,ui];ho.prototype.g=bi([0,Rs,to,[0,Rs],Ms,Bs,co,uo]),Ni[514774813]=co,Ni[518928384]=uo;var lo=class extends nr{constructor(t){super(t)}},fo=Ti(456383383,lo);Ni[456383383]=[0,Rs,ws],Ui[456383383]=1;var po=class extends nr{constructor(t){super(t)}},go=Ti(476348187,po);Ni[476348187]=[0,Rs,As],Ui[476348187]=1;var mo=class extends nr{constructor(t){super(t)}},yo=class extends nr{constructor(t){super(t)}},_o=[0,Ei,-1],vo=Ti(458105876,class extends nr{constructor(t){super(t)}g(){var t=this.u;const e=et(t);const n=2&e;return t=function(t,e,n){var r=yo;const i=2&e;let s=!1;if(null==n){if(i)return Ne();n=[]}else if(n.constructor===Me){if(0==(2&n.M)||i)return n;n=n.Z()}else Array.isArray(n)?s=!!(2&tt(n)):n=[];if(i){if(!n.length)return Ne();s||(s=!0,rt(n))}else s&&(s=!1,n=on(n));return s||(64&tt(n)?Q(n,32):32&e&&Z(n,32)),qe(t,e,2,r=new Me(n,r,se,void 0)),r}(t,e,Ye(t,e,2)),!n&&yo&&(t.va=!0),t}});Ni[458105876]=[0,_o,Hr,[!0,ii,[0,pi,-1,fi]]],Ui[458105876]=1;var Eo=class extends nr{constructor(t){super(t)}},wo=Ti(458105758,Eo);Ni[458105758]=[0,Rs,pi,_o],Ui[458105758]=1;var To=class extends nr{constructor(t){super(t)}},Ao=Ti(443442058,To);Ni[443442058]=[0,Rs,pi,ai,ei,fi,-1],Ui[443442058]=1,Ui[514774813]=1;var bo=class extends nr{constructor(t){super(t)}},ko=Ti(516587230,bo);function So(t,e){return e=e?e.clone():new Es,void 0!==t.displayNamesLocale?$e(e,1,ne(t.displayNamesLocale)):void 0===t.displayNamesLocale&&$e(e,1),void 0!==t.maxResults?xn(e,2,t.maxResults):\"maxResults\"in t&&$e(e,2),void 0!==t.scoreThreshold?Ln(e,3,t.scoreThreshold):\"scoreThreshold\"in t&&$e(e,3),void 0!==t.categoryAllowlist?Rn(e,4,t.categoryAllowlist):\"categoryAllowlist\"in t&&$e(e,4),void 0!==t.categoryDenylist?Rn(e,5,t.categoryDenylist):\"categoryDenylist\"in t&&$e(e,5),e}function xo(t,e=-1,n=\"\"){return{categories:t.map((t=>({index:Tn(An(t,1),0)??-1,score:bn(t,2)??0,categoryName:kn(t,3)??\"\",displayName:kn(t,4)??\"\"}))),headIndex:e,headName:n}}function Lo(t){var e=en(t,3,Ht,tn()),n=en(t,2,Yt,tn()),r=en(t,1,re,tn()),i=en(t,9,re,tn());const s={categories:[],keypoints:[]};for(let t=0;t<e.length;t++)s.categories.push({score:e[t],index:n[t]??-1,categoryName:r[t]??\"\",displayName:i[t]??\"\"});if((e=pn(t,is,4)?.h())&&(s.boundingBox={originX:An(e,1)??0,originY:An(e,2)??0,width:An(e,3)??0,height:An(e,4)??0,angle:0}),pn(t,is,4)?.g().length)for(const e of pn(t,is,4).g())s.keypoints.push({x:Ze(e,1)??0,y:Ze(e,2)??0,score:Ze(e,4)??0,label:re(ze(e,3))??\"\"});return s}function Ro(t){const e=[];for(const n of mn(t,hs,1))e.push({x:bn(n,1)??0,y:bn(n,2)??0,z:bn(n,3)??0,visibility:bn(n,4)??0});return e}function Fo(t){const e=[];for(const n of mn(t,os,1))e.push({x:bn(n,1)??0,y:bn(n,2)??0,z:bn(n,3)??0,visibility:bn(n,4)??0});return e}function Mo(t){return Array.from(t,(t=>t>127?t-256:t))}function Io(t,e){if(t.length!==e.length)throw Error(`Cannot compute cosine similarity between embeddings of different sizes (${t.length} vs. ${e.length}).`);let n=0,r=0,i=0;for(let s=0;s<t.length;s++)n+=t[s]*e[s],r+=t[s]*t[s],i+=e[s]*e[s];if(r<=0||i<=0)throw Error(\"Cannot compute cosine similarity on embedding with 0 norm.\");return n/Math.sqrt(r*i)}let Po;Ni[516587230]=[0,Rs,co,uo,ei],Ui[516587230]=1,Ui[518928384]=1;const Oo=new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11]);async function Co(){if(void 0===Po)try{await WebAssembly.instantiate(Oo),Po=!0}catch{Po=!1}return Po}async function No(t,e=\"\"){const n=await Co()?\"wasm_internal\":\"wasm_nosimd_internal\";return{wasmLoaderPath:`${e}/${t}_${n}.js`,wasmBinaryPath:`${e}/${t}_${n}.wasm`}}var Uo=class{};function Do(){var t=navigator;return\"undefined\"!=typeof OffscreenCanvas&&(!function(t=navigator){return(t=t.userAgent).includes(\"Safari\")&&!t.includes(\"Chrome\")}(t)||!!((t=t.userAgent.match(/Version\\/([\\d]+).*Safari/))&&t.length>=1&&Number(t[1])>=17))}async function Bo(t){if(\"function\"!=typeof importScripts){const e=document.createElement(\"script\");return e.src=t.toString(),e.crossOrigin=\"anonymous\",new Promise(((t,n)=>{e.addEventListener(\"load\",(()=>{t()}),!1),e.addEventListener(\"error\",(t=>{n(t)}),!1),document.body.appendChild(e)}))}importScripts(t.toString())}function Go(t){return void 0!==t.videoWidth?[t.videoWidth,t.videoHeight]:void 0!==t.naturalWidth?[t.naturalWidth,t.naturalHeight]:void 0!==t.displayWidth?[t.displayWidth,t.displayHeight]:[t.width,t.height]}function jo(t,e,n){t.m||console.error(\"No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target\"),n(e=t.i.stringToNewUTF8(e)),t.i._free(e)}function Vo(t,e,n){if(!t.i.canvas)throw Error(\"No OpenGL canvas configured.\");if(n?t.i._bindTextureToStream(n):t.i._bindTextureToCanvas(),!(n=t.i.canvas.getContext(\"webgl2\")||t.i.canvas.getContext(\"webgl\")))throw Error(\"Failed to obtain WebGL context from the provided canvas. `getContext()` should only be invoked with `webgl` or `webgl2`.\");t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!0),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,e),t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!1);const[r,i]=Go(e);return!t.l||r===t.i.canvas.width&&i===t.i.canvas.height||(t.i.canvas.width=r,t.i.canvas.height=i),[r,i]}function Xo(t,e,n){t.m||console.error(\"No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target\");const r=new Uint32Array(e.length);for(let n=0;n<e.length;n++)r[n]=t.i.stringToNewUTF8(e[n]);e=t.i._malloc(4*r.length),t.i.HEAPU32.set(r,e>>2),n(e);for(const e of r)t.i._free(e);t.i._free(e)}function Ho(t,e,n){t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=n}function Wo(t,e,n){let r=[];t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=(t,e,i)=>{e?(n(r,i),r=[]):r.push(t)}}Uo.forVisionTasks=function(t){return No(\"vision\",t)},Uo.forTextTasks=function(t){return No(\"text\",t)},Uo.forGenAiExperimentalTasks=function(t){return No(\"genai_experimental\",t)},Uo.forGenAiTasks=function(t){return No(\"genai\",t)},Uo.forAudioTasks=function(t){return No(\"audio\",t)},Uo.isSimdSupported=function(){return Co()};async function zo(t,e,n,r){return t=await(async(t,e,n,r,i)=>{if(e&&await Bo(e),!self.ModuleFactory)throw Error(\"ModuleFactory not set.\");if(n&&(await Bo(n),!self.ModuleFactory))throw Error(\"ModuleFactory not set.\");return self.Module&&i&&((e=self.Module).locateFile=i.locateFile,i.mainScriptUrlOrBlob&&(e.mainScriptUrlOrBlob=i.mainScriptUrlOrBlob)),i=await self.ModuleFactory(self.Module||i),self.ModuleFactory=self.Module=void 0,new t(i,r)})(t,n.wasmLoaderPath,n.assetLoaderPath,e,{locateFile:t=>t.endsWith(\".wasm\")?n.wasmBinaryPath.toString():n.assetBinaryPath&&t.endsWith(\".data\")?n.assetBinaryPath.toString():t}),await t.o(r),t}function Ko(t,e){const n=pn(t.baseOptions,Ss,1)||new Ss;\"string\"==typeof e?($e(n,2,ne(e)),$e(n,1)):e instanceof Uint8Array&&($e(n,1,lt(e,!1,!1)),$e(n,2)),yn(t.baseOptions,0,1,n)}function Yo(t){try{const e=t.H.length;if(1===e)throw Error(t.H[0].message);if(e>1)throw Error(\"Encountered multiple errors: \"+t.H.map((t=>t.message)).join(\", \"))}finally{t.H=[]}}function $o(t,e){t.B=Math.max(t.B,e)}function qo(t,e){t.A=new Vi,Bi(t.A,\"PassThroughCalculator\"),Gi(t.A,\"free_memory\"),ji(t.A,\"free_memory_unused_out\"),Ki(e,\"free_memory\"),zi(e,t.A)}function Jo(t,e){Gi(t.A,e),ji(t.A,e+\"_unused_out\")}function Zo(t){t.g.addBoolToStream(!0,\"free_memory\",t.B)}var Qo=class{constructor(t){this.g=t,this.H=[],this.B=0,this.g.setAutoRenderToScreen(!1)}l(t,e=!0){if(e){const e=t.baseOptions||{};if(t.baseOptions?.modelAssetBuffer&&t.baseOptions?.modelAssetPath)throw Error(\"Cannot set both baseOptions.modelAssetPath and baseOptions.modelAssetBuffer\");if(!(pn(this.baseOptions,Ss,1)?.g()||pn(this.baseOptions,Ss,1)?.h()||t.baseOptions?.modelAssetBuffer||t.baseOptions?.modelAssetPath))throw Error(\"Either baseOptions.modelAssetPath or baseOptions.modelAssetBuffer must be set\");if(function(t,e){let n=pn(t.baseOptions,bs,3);if(!n){var r=n=new bs,i=new Mi;_n(r,4,ks,i)}\"delegate\"in e&&(\"GPU\"===e.delegate?(e=n,r=new Li,_n(e,2,ks,r)):(e=n,r=new Mi,_n(e,4,ks,r))),yn(t.baseOptions,0,3,n)}(this,e),e.modelAssetPath)return fetch(e.modelAssetPath.toString()).then((t=>{if(t.ok)return t.arrayBuffer();throw Error(`Failed to fetch model: ${e.modelAssetPath} (${t.status})`)})).then((t=>{try{this.g.i.FS_unlink(\"/model.dat\")}catch{}this.g.i.FS_createDataFile(\"/\",\"model.dat\",new Uint8Array(t),!0,!1,!1),Ko(this,\"/model.dat\"),this.m(),this.J()}));if(e.modelAssetBuffer instanceof Uint8Array)Ko(this,e.modelAssetBuffer);else if(e.modelAssetBuffer)return async function(t){const e=[];for(var n=0;;){const{done:r,value:i}=await t.read();if(r)break;e.push(i),n+=i.length}if(0===e.length)return new Uint8Array(0);if(1===e.length)return e[0];t=new Uint8Array(n),n=0;for(const r of e)t.set(r,n),n+=r.length;return t}(e.modelAssetBuffer).then((t=>{Ko(this,t),this.m(),this.J()}))}return this.m(),this.J(),Promise.resolve()}J(){}ga(){let t;if(this.g.ga((e=>{t=Ji(e)})),!t)throw Error(\"Failed to retrieve CalculatorGraphConfig\");return t}setGraph(t,e){this.g.attachErrorListener(((t,e)=>{this.H.push(Error(e))})),this.g.Oa(),this.g.setGraph(t,e),this.A=void 0,Yo(this)}finishProcessing(){this.g.finishProcessing(),Yo(this)}close(){this.A=void 0,this.g.closeGraph()}};function ta(t,e){if(!t)throw Error(`Unable to obtain required WebGL resource: ${e}`);return t}Qo.prototype.close=Qo.prototype.close,function(e,n){e=e.split(\".\");var r,i=t;e[0]in i||void 0===i.execScript||i.execScript(\"var \"+e[0]);for(;e.length&&(r=e.shift());)e.length||void 0===n?i=i[r]&&i[r]!==Object.prototype[r]?i[r]:i[r]={}:i[r]=n}(\"TaskRunner\",Qo);class ea{constructor(t,e,n,r){this.g=t,this.h=e,this.m=n,this.l=r}bind(){this.g.bindVertexArray(this.h)}close(){this.g.deleteVertexArray(this.h),this.g.deleteBuffer(this.m),this.g.deleteBuffer(this.l)}}function na(t,e,n){const r=t.g;if(n=ta(r.createShader(n),\"Failed to create WebGL shader\"),r.shaderSource(n,e),r.compileShader(n),!r.getShaderParameter(n,r.COMPILE_STATUS))throw Error(`Could not compile WebGL shader: ${r.getShaderInfoLog(n)}`);return r.attachShader(t.h,n),n}function ra(t,e){const n=t.g,r=ta(n.createVertexArray(),\"Failed to create vertex array\");n.bindVertexArray(r);const i=ta(n.createBuffer(),\"Failed to create buffer\");n.bindBuffer(n.ARRAY_BUFFER,i),n.enableVertexAttribArray(t.R),n.vertexAttribPointer(t.R,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW);const s=ta(n.createBuffer(),\"Failed to create buffer\");return n.bindBuffer(n.ARRAY_BUFFER,s),n.enableVertexAttribArray(t.J),n.vertexAttribPointer(t.J,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array(e?[0,1,0,0,1,0,1,1]:[0,0,0,1,1,1,1,0]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.bindVertexArray(null),new ea(n,r,i,s)}function ia(t,e){if(t.g){if(e!==t.g)throw Error(\"Cannot change GL context once initialized\")}else t.g=e}function sa(t,e,n,r){return ia(t,e),t.h||(t.m(),t.C()),n?(t.s||(t.s=ra(t,!0)),n=t.s):(t.v||(t.v=ra(t,!1)),n=t.v),e.useProgram(t.h),n.bind(),t.l(),t=r(),n.g.bindVertexArray(null),t}function oa(t,e,n){return ia(t,e),t=ta(e.createTexture(),\"Failed to create texture\"),e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,n??e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,n??e.LINEAR),e.bindTexture(e.TEXTURE_2D,null),t}function aa(t,e,n){ia(t,e),t.A||(t.A=ta(e.createFramebuffer(),\"Failed to create framebuffe.\")),e.bindFramebuffer(e.FRAMEBUFFER,t.A),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,n,0)}function ha(t){t.g?.bindFramebuffer(t.g.FRAMEBUFFER,null)}var ca=class{H(){return\"\\n  precision mediump float;\\n  varying vec2 vTex;\\n  uniform sampler2D inputTexture;\\n  void main() {\\n    gl_FragColor = texture2D(inputTexture, vTex);\\n  }\\n \"}m(){const t=this.g;if(this.h=ta(t.createProgram(),\"Failed to create WebGL program\"),this.da=na(this,\"\\n  attribute vec2 aVertex;\\n  attribute vec2 aTex;\\n  varying vec2 vTex;\\n  void main(void) {\\n    gl_Position = vec4(aVertex, 0.0, 1.0);\\n    vTex = aTex;\\n  }\",t.VERTEX_SHADER),this.ca=na(this,this.H(),t.FRAGMENT_SHADER),t.linkProgram(this.h),!t.getProgramParameter(this.h,t.LINK_STATUS))throw Error(`Error during program linking: ${t.getProgramInfoLog(this.h)}`);this.R=t.getAttribLocation(this.h,\"aVertex\"),this.J=t.getAttribLocation(this.h,\"aTex\")}C(){}l(){}close(){if(this.h){const t=this.g;t.deleteProgram(this.h),t.deleteShader(this.da),t.deleteShader(this.ca)}this.A&&this.g.deleteFramebuffer(this.A),this.v&&this.v.close(),this.s&&this.s.close()}};var ua=class extends ca{H(){return\"\\n  precision mediump float;\\n  uniform sampler2D backgroundTexture;\\n  uniform sampler2D maskTexture;\\n  uniform sampler2D colorMappingTexture;\\n  varying vec2 vTex;\\n  void main() {\\n    vec4 backgroundColor = texture2D(backgroundTexture, vTex);\\n    float category = texture2D(maskTexture, vTex).r;\\n    vec4 categoryColor = texture2D(colorMappingTexture, vec2(category, 0.0));\\n    gl_FragColor = mix(backgroundColor, categoryColor, categoryColor.a);\\n  }\\n \"}C(){const t=this.g;t.activeTexture(t.TEXTURE1),this.B=oa(this,t,t.LINEAR),t.activeTexture(t.TEXTURE2),this.j=oa(this,t,t.NEAREST)}m(){super.m();const t=this.g;this.L=ta(t.getUniformLocation(this.h,\"backgroundTexture\"),\"Uniform location\"),this.V=ta(t.getUniformLocation(this.h,\"colorMappingTexture\"),\"Uniform location\"),this.K=ta(t.getUniformLocation(this.h,\"maskTexture\"),\"Uniform location\")}l(){super.l();const t=this.g;t.uniform1i(this.K,0),t.uniform1i(this.L,1),t.uniform1i(this.V,2)}close(){this.B&&this.g.deleteTexture(this.B),this.j&&this.g.deleteTexture(this.j),super.close()}},la=class extends ca{H(){return\"\\n  precision mediump float;\\n  uniform sampler2D maskTexture;\\n  uniform sampler2D defaultTexture;\\n  uniform sampler2D overlayTexture;\\n  varying vec2 vTex;\\n  void main() {\\n    float confidence = texture2D(maskTexture, vTex).r;\\n    vec4 defaultColor = texture2D(defaultTexture, vTex);\\n    vec4 overlayColor = texture2D(overlayTexture, vTex);\\n    // Apply the alpha from the overlay and merge in the default color\\n    overlayColor = mix(defaultColor, overlayColor, overlayColor.a);\\n    gl_FragColor = mix(defaultColor, overlayColor, confidence);\\n  }\\n \"}C(){const t=this.g;t.activeTexture(t.TEXTURE1),this.j=oa(this,t),t.activeTexture(t.TEXTURE2),this.B=oa(this,t)}m(){super.m();const t=this.g;this.K=ta(t.getUniformLocation(this.h,\"defaultTexture\"),\"Uniform location\"),this.L=ta(t.getUniformLocation(this.h,\"overlayTexture\"),\"Uniform location\"),this.I=ta(t.getUniformLocation(this.h,\"maskTexture\"),\"Uniform location\")}l(){super.l();const t=this.g;t.uniform1i(this.I,0),t.uniform1i(this.K,1),t.uniform1i(this.L,2)}close(){this.j&&this.g.deleteTexture(this.j),this.B&&this.g.deleteTexture(this.B),super.close()}};function fa(t,e){switch(e){case 0:return t.g.find((t=>t instanceof Uint8Array));case 1:return t.g.find((t=>t instanceof Float32Array));case 2:return t.g.find((t=>\"undefined\"!=typeof WebGLTexture&&t instanceof WebGLTexture));default:throw Error(`Type is not supported: ${e}`)}}function da(t){var e=fa(t,1);if(!e){if(e=fa(t,0))e=new Float32Array(e).map((t=>t/255));else{e=new Float32Array(t.width*t.height);const r=ga(t);var n=ya(t);if(aa(n,r,pa(t)),\"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod\".split(\";\").includes(navigator.platform)||navigator.userAgent.includes(\"Mac\")&&\"ontouchend\"in self.document){n=new Float32Array(t.width*t.height*4),r.readPixels(0,0,t.width,t.height,r.RGBA,r.FLOAT,n);for(let t=0,r=0;t<e.length;++t,r+=4)e[t]=n[r]}else r.readPixels(0,0,t.width,t.height,r.RED,r.FLOAT,e)}t.g.push(e)}return e}function pa(t){let e=fa(t,2);if(!e){const n=ga(t);e=_a(t);const r=da(t),i=ma(t);n.texImage2D(n.TEXTURE_2D,0,i,t.width,t.height,0,n.RED,n.FLOAT,r),va(t)}return e}function ga(t){if(!t.canvas)throw Error(\"Conversion to different image formats require that a canvas is passed when initializing the image.\");return t.h||(t.h=ta(t.canvas.getContext(\"webgl2\"),\"You cannot use a canvas that is already bound to a different type of rendering context.\")),t.h}function ma(t){if(t=ga(t),!Ea)if(t.getExtension(\"EXT_color_buffer_float\")&&t.getExtension(\"OES_texture_float_linear\")&&t.getExtension(\"EXT_float_blend\"))Ea=t.R32F;else{if(!t.getExtension(\"EXT_color_buffer_half_float\"))throw Error(\"GPU does not fully support 4-channel float32 or float16 formats\");Ea=t.R16F}return Ea}function ya(t){return t.l||(t.l=new ca),t.l}function _a(t){const e=ga(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=fa(t,2);return n||(n=oa(ya(t),e,t.m?e.LINEAR:e.NEAREST),t.g.push(n),t.j=!0),e.bindTexture(e.TEXTURE_2D,n),n}function va(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}var Ea,wa=class{constructor(t,e,n,r,i,s,o){this.g=t,this.m=e,this.j=n,this.canvas=r,this.l=i,this.width=s,this.height=o,this.j&&(0===--Ta&&console.error(\"You seem to be creating MPMask instances without invoking .close(). This leaks resources.\"))}Ja(){return!!fa(this,0)}ma(){return!!fa(this,1)}S(){return!!fa(this,2)}la(){return(e=fa(t=this,0))||(e=da(t),e=new Uint8Array(e.map((t=>255*t))),t.g.push(e)),e;var t,e}ka(){return da(this)}N(){return pa(this)}clone(){const t=[];for(const e of this.g){let n;if(e instanceof Uint8Array)n=new Uint8Array(e);else if(e instanceof Float32Array)n=new Float32Array(e);else{if(!(e instanceof WebGLTexture))throw Error(`Type is not supported: ${e}`);{const t=ga(this),e=ya(this);t.activeTexture(t.TEXTURE1),n=oa(e,t,this.m?t.LINEAR:t.NEAREST),t.bindTexture(t.TEXTURE_2D,n);const r=ma(this);t.texImage2D(t.TEXTURE_2D,0,r,this.width,this.height,0,t.RED,t.FLOAT,null),t.bindTexture(t.TEXTURE_2D,null),aa(e,t,n),sa(e,t,!1,(()=>{_a(this),t.clearColor(0,0,0,0),t.clear(t.COLOR_BUFFER_BIT),t.drawArrays(t.TRIANGLE_FAN,0,4),va(this)})),ha(e),va(this)}}t.push(n)}return new wa(t,this.m,this.S(),this.canvas,this.l,this.width,this.height)}close(){this.j&&ga(this).deleteTexture(fa(this,2)),Ta=-1}};wa.prototype.close=wa.prototype.close,wa.prototype.clone=wa.prototype.clone,wa.prototype.getAsWebGLTexture=wa.prototype.N,wa.prototype.getAsFloat32Array=wa.prototype.ka,wa.prototype.getAsUint8Array=wa.prototype.la,wa.prototype.hasWebGLTexture=wa.prototype.S,wa.prototype.hasFloat32Array=wa.prototype.ma,wa.prototype.hasUint8Array=wa.prototype.Ja;var Ta=250;const Aa={color:\"white\",lineWidth:4,radius:6};function ba(t){return{...Aa,fillColor:(t=t||{}).color,...t}}function ka(t,e){return t instanceof Function?t(e):t}function Sa(t,e,n){return Math.max(Math.min(e,n),Math.min(Math.max(e,n),t))}function xa(t){if(!t.l)throw Error(\"CPU rendering requested but CanvasRenderingContext2D not provided.\");return t.l}function La(t){if(!t.j)throw Error(\"GPU rendering requested but WebGL2RenderingContext not provided.\");return t.j}function Ra(t,e,n){if(e.S())n(e.N());else{const r=e.ma()?e.ka():e.la();t.m=t.m??new ca;const i=La(t);n((t=new wa([r],e.m,!1,i.canvas,t.m,e.width,e.height)).N()),t.close()}}function Fa(t,e,n,r){const i=function(t){return t.g||(t.g=new ua),t.g}(t),s=La(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n;sa(i,s,!0,(()=>{!function(t,e,n,r){const i=t.g;if(i.activeTexture(i.TEXTURE0),i.bindTexture(i.TEXTURE_2D,e),i.activeTexture(i.TEXTURE1),i.bindTexture(i.TEXTURE_2D,t.B),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,i.RGBA,i.UNSIGNED_BYTE,n),t.I&&function(t,e){if(t!==e)return!1;t=t.entries(),e=e.entries();for(const[r,i]of t){t=r;const s=i;var n=e.next();if(n.done)return!1;const[o,a]=n.value;if(n=a,t!==o||s[0]!==n[0]||s[1]!==n[1]||s[2]!==n[2]||s[3]!==n[3])return!1}return!!e.next().done}(t.I,r))i.activeTexture(i.TEXTURE2),i.bindTexture(i.TEXTURE_2D,t.j);else{t.I=r;const e=Array(1024).fill(0);r.forEach(((t,n)=>{if(4!==t.length)throw Error(`Color at index ${n} is not a four-channel value.`);e[4*n]=t[0],e[4*n+1]=t[1],e[4*n+2]=t[2],e[4*n+3]=t[3]})),i.activeTexture(i.TEXTURE2),i.bindTexture(i.TEXTURE_2D,t.j),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,256,1,0,i.RGBA,i.UNSIGNED_BYTE,new Uint8Array(e))}}(i,e,o,r),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT),s.drawArrays(s.TRIANGLE_FAN,0,4);const t=i.g;t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,null)}))}function Ma(t,e,n,r){const i=La(t),s=function(t){return t.h||(t.h=new la),t.h}(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n,a=Array.isArray(r)?new ImageData(new Uint8ClampedArray(r),1,1):r;sa(s,i,!0,(()=>{var t=s.g;t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,s.j),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,o),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,s.B),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,a),i.clearColor(0,0,0,0),i.clear(i.COLOR_BUFFER_BIT),i.drawArrays(i.TRIANGLE_FAN,0,4),i.bindTexture(i.TEXTURE_2D,null),(t=s.g).activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,null)}))}var Ia=class{constructor(t,e){t instanceof CanvasRenderingContext2D||t instanceof OffscreenCanvasRenderingContext2D?(this.l=t,this.j=e):this.j=t}Ca(t,e){if(t){var n=xa(this);e=ba(e),n.save();var r=n.canvas,i=0;for(const s of t)n.fillStyle=ka(e.fillColor,{index:i,from:s}),n.strokeStyle=ka(e.color,{index:i,from:s}),n.lineWidth=ka(e.lineWidth,{index:i,from:s}),(t=new Path2D).arc(s.x*r.width,s.y*r.height,ka(e.radius,{index:i,from:s}),0,2*Math.PI),n.fill(t),n.stroke(t),++i;n.restore()}}Ba(t,e,n){if(t&&e){var r=xa(this);n=ba(n),r.save();var i=r.canvas,s=0;for(const o of e){r.beginPath(),e=t[o.start];const a=t[o.end];e&&a&&(r.strokeStyle=ka(n.color,{index:s,from:e,to:a}),r.lineWidth=ka(n.lineWidth,{index:s,from:e,to:a}),r.moveTo(e.x*i.width,e.y*i.height),r.lineTo(a.x*i.width,a.y*i.height)),++s,r.stroke()}r.restore()}}ya(t,e){const n=xa(this);e=ba(e),n.save(),n.beginPath(),n.lineWidth=ka(e.lineWidth,{}),n.strokeStyle=ka(e.color,{}),n.fillStyle=ka(e.fillColor,{}),n.moveTo(t.originX,t.originY),n.lineTo(t.originX+t.width,t.originY),n.lineTo(t.originX+t.width,t.originY+t.height),n.lineTo(t.originX,t.originY+t.height),n.lineTo(t.originX,t.originY),n.stroke(),n.fill(),n.restore()}za(t,e,n=[0,0,0,255]){this.l?function(t,e,n,r){const i=La(t);Ra(t,e,(e=>{Fa(t,e,n,r),(e=xa(t)).drawImage(i.canvas,0,0,e.canvas.width,e.canvas.height)}))}(this,t,n,e):Fa(this,t.N(),n,e)}Aa(t,e,n){this.l?function(t,e,n,r){const i=La(t);Ra(t,e,(e=>{Ma(t,e,n,r),(e=xa(t)).drawImage(i.canvas,0,0,e.canvas.width,e.canvas.height)}))}(this,t,e,n):Ma(this,t.N(),e,n)}close(){this.g?.close(),this.g=void 0,this.h?.close(),this.h=void 0,this.m?.close(),this.m=void 0}};function Pa(t,e){switch(e){case 0:return t.g.find((t=>t instanceof ImageData));case 1:return t.g.find((t=>\"undefined\"!=typeof ImageBitmap&&t instanceof ImageBitmap));case 2:return t.g.find((t=>\"undefined\"!=typeof WebGLTexture&&t instanceof WebGLTexture));default:throw Error(`Type is not supported: ${e}`)}}function Oa(t){var e=Pa(t,0);if(!e){e=Na(t);const n=Ua(t),r=new Uint8Array(t.width*t.height*4);aa(n,e,Ca(t)),e.readPixels(0,0,t.width,t.height,e.RGBA,e.UNSIGNED_BYTE,r),ha(n),e=new ImageData(new Uint8ClampedArray(r.buffer),t.width,t.height),t.g.push(e)}return e}function Ca(t){let e=Pa(t,2);if(!e){const n=Na(t);e=Da(t);const r=Pa(t,1)||Oa(t);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,r),Ba(t)}return e}function Na(t){if(!t.canvas)throw Error(\"Conversion to different image formats require that a canvas is passed when iniitializing the image.\");return t.h||(t.h=ta(t.canvas.getContext(\"webgl2\"),\"You cannot use a canvas that is already bound to a different type of rendering context.\")),t.h}function Ua(t){return t.l||(t.l=new ca),t.l}function Da(t){const e=Na(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=Pa(t,2);return n||(n=oa(Ua(t),e),t.g.push(n),t.m=!0),e.bindTexture(e.TEXTURE_2D,n),n}function Ba(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}function Ga(t){const e=Na(t);return sa(Ua(t),e,!0,(()=>function(t,e){const n=t.canvas;if(n.width===t.width&&n.height===t.height)return e();const r=n.width,i=n.height;return n.width=t.width,n.height=t.height,t=e(),n.width=r,n.height=i,t}(t,(()=>{if(e.bindFramebuffer(e.FRAMEBUFFER,null),e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT),e.drawArrays(e.TRIANGLE_FAN,0,4),!(t.canvas instanceof OffscreenCanvas))throw Error(\"Conversion to ImageBitmap requires that the MediaPipe Tasks is initialized with an OffscreenCanvas\");return t.canvas.transferToImageBitmap()}))))}Ia.prototype.close=Ia.prototype.close,Ia.prototype.drawConfidenceMask=Ia.prototype.Aa,Ia.prototype.drawCategoryMask=Ia.prototype.za,Ia.prototype.drawBoundingBox=Ia.prototype.ya,Ia.prototype.drawConnectors=Ia.prototype.Ba,Ia.prototype.drawLandmarks=Ia.prototype.Ca,Ia.lerp=function(t,e,n,r,i){return Sa(r*(1-(t-e)/(n-e))+i*(1-(n-t)/(n-e)),r,i)},Ia.clamp=Sa;var ja=class{constructor(t,e,n,r,i,s,o){this.g=t,this.j=e,this.m=n,this.canvas=r,this.l=i,this.width=s,this.height=o,(this.j||this.m)&&(0===--Va&&console.error(\"You seem to be creating MPImage instances without invoking .close(). This leaks resources.\"))}Ia(){return!!Pa(this,0)}na(){return!!Pa(this,1)}S(){return!!Pa(this,2)}Ga(){return Oa(this)}Fa(){var t=Pa(this,1);return t||(Ca(this),Da(this),t=Ga(this),Ba(this),this.g.push(t),this.j=!0),t}N(){return Ca(this)}clone(){const t=[];for(const e of this.g){let n;if(e instanceof ImageData)n=new ImageData(e.data,this.width,this.height);else if(e instanceof WebGLTexture){const t=Na(this),e=Ua(this);t.activeTexture(t.TEXTURE1),n=oa(e,t),t.bindTexture(t.TEXTURE_2D,n),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,this.width,this.height,0,t.RGBA,t.UNSIGNED_BYTE,null),t.bindTexture(t.TEXTURE_2D,null),aa(e,t,n),sa(e,t,!1,(()=>{Da(this),t.clearColor(0,0,0,0),t.clear(t.COLOR_BUFFER_BIT),t.drawArrays(t.TRIANGLE_FAN,0,4),Ba(this)})),ha(e),Ba(this)}else{if(!(e instanceof ImageBitmap))throw Error(`Type is not supported: ${e}`);Ca(this),Da(this),n=Ga(this),Ba(this)}t.push(n)}return new ja(t,this.na(),this.S(),this.canvas,this.l,this.width,this.height)}close(){this.j&&Pa(this,1).close(),this.m&&Na(this).deleteTexture(Pa(this,2)),Va=-1}};ja.prototype.close=ja.prototype.close,ja.prototype.clone=ja.prototype.clone,ja.prototype.getAsWebGLTexture=ja.prototype.N,ja.prototype.getAsImageBitmap=ja.prototype.Fa,ja.prototype.getAsImageData=ja.prototype.Ga,ja.prototype.hasWebGLTexture=ja.prototype.S,ja.prototype.hasImageBitmap=ja.prototype.na,ja.prototype.hasImageData=ja.prototype.Ia;var Va=250;function Xa(...t){return t.map((([t,e])=>({start:t,end:e})))}const Ha=function(t){return class extends t{Oa(){this.i._registerModelResourcesGraphService()}}}((Wa=class{constructor(t,e){this.l=!0,this.i=t,this.g=null,this.h=0,this.m=\"function\"==typeof this.i._addIntToInputStream,void 0!==e?this.i.canvas=e:Do()?this.i.canvas=new OffscreenCanvas(1,1):(console.warn(\"OffscreenCanvas not supported and GraphRunner constructor glCanvas parameter is undefined. Creating backup canvas.\"),this.i.canvas=document.createElement(\"canvas\"))}async initializeGraph(t){const e=await(await fetch(t)).arrayBuffer();t=!(t.endsWith(\".pbtxt\")||t.endsWith(\".textproto\")),this.setGraph(new Uint8Array(e),t)}setGraphFromString(t){this.setGraph((new TextEncoder).encode(t),!1)}setGraph(t,e){const n=t.length,r=this.i._malloc(n);this.i.HEAPU8.set(t,r),e?this.i._changeBinaryGraph(n,r):this.i._changeTextGraph(n,r),this.i._free(r)}configureAudio(t,e,n,r,i){this.i._configureAudio||console.warn('Attempting to use configureAudio without support for input audio. Is build dep \":gl_graph_runner_audio\" missing?'),jo(this,r||\"input_audio\",(r=>{jo(this,i=i||\"audio_header\",(i=>{this.i._configureAudio(r,i,t,e,n)}))}))}setAutoResizeCanvas(t){this.l=t}setAutoRenderToScreen(t){this.i._setAutoRenderToScreen(t)}setGpuBufferVerticalFlip(t){this.i.gpuOriginForWebTexturesIsBottomLeft=t}ga(t){Ho(this,\"__graph_config__\",(e=>{t(e)})),jo(this,\"__graph_config__\",(t=>{this.i._getGraphConfig(t,void 0)})),delete this.i.simpleListeners.__graph_config__}attachErrorListener(t){this.i.errorListener=t}attachEmptyPacketListener(t,e){this.i.emptyPacketListeners=this.i.emptyPacketListeners||{},this.i.emptyPacketListeners[t]=e}addAudioToStream(t,e,n){this.addAudioToStreamWithShape(t,0,0,e,n)}addAudioToStreamWithShape(t,e,n,r,i){const s=4*t.length;this.h!==s&&(this.g&&this.i._free(this.g),this.g=this.i._malloc(s),this.h=s),this.i.HEAPF32.set(t,this.g/4),jo(this,r,(t=>{this.i._addAudioToInputStream(this.g,e,n,t,i)}))}addGpuBufferToStream(t,e,n){jo(this,e,(e=>{const[r,i]=Vo(this,t,e);this.i._addBoundTextureToStream(e,r,i,n)}))}addBoolToStream(t,e,n){jo(this,e,(e=>{this.i._addBoolToInputStream(t,e,n)}))}addDoubleToStream(t,e,n){jo(this,e,(e=>{this.i._addDoubleToInputStream(t,e,n)}))}addFloatToStream(t,e,n){jo(this,e,(e=>{this.i._addFloatToInputStream(t,e,n)}))}addIntToStream(t,e,n){jo(this,e,(e=>{this.i._addIntToInputStream(t,e,n)}))}addUintToStream(t,e,n){jo(this,e,(e=>{this.i._addUintToInputStream(t,e,n)}))}addStringToStream(t,e,n){jo(this,e,(e=>{jo(this,t,(t=>{this.i._addStringToInputStream(t,e,n)}))}))}addStringRecordToStream(t,e,n){jo(this,e,(e=>{Xo(this,Object.keys(t),(r=>{Xo(this,Object.values(t),(i=>{this.i._addFlatHashMapToInputStream(r,i,Object.keys(t).length,e,n)}))}))}))}addProtoToStream(t,e,n,r){jo(this,n,(n=>{jo(this,e,(e=>{const i=this.i._malloc(t.length);this.i.HEAPU8.set(t,i),this.i._addProtoToInputStream(i,t.length,e,n,r),this.i._free(i)}))}))}addEmptyPacketToStream(t,e){jo(this,t,(t=>{this.i._addEmptyPacketToInputStream(t,e)}))}addBoolVectorToStream(t,e,n){jo(this,e,(e=>{const r=this.i._allocateBoolVector(t.length);if(!r)throw Error(\"Unable to allocate new bool vector on heap.\");for(const e of t)this.i._addBoolVectorEntry(r,e);this.i._addBoolVectorToInputStream(r,e,n)}))}addDoubleVectorToStream(t,e,n){jo(this,e,(e=>{const r=this.i._allocateDoubleVector(t.length);if(!r)throw Error(\"Unable to allocate new double vector on heap.\");for(const e of t)this.i._addDoubleVectorEntry(r,e);this.i._addDoubleVectorToInputStream(r,e,n)}))}addFloatVectorToStream(t,e,n){jo(this,e,(e=>{const r=this.i._allocateFloatVector(t.length);if(!r)throw Error(\"Unable to allocate new float vector on heap.\");for(const e of t)this.i._addFloatVectorEntry(r,e);this.i._addFloatVectorToInputStream(r,e,n)}))}addIntVectorToStream(t,e,n){jo(this,e,(e=>{const r=this.i._allocateIntVector(t.length);if(!r)throw Error(\"Unable to allocate new int vector on heap.\");for(const e of t)this.i._addIntVectorEntry(r,e);this.i._addIntVectorToInputStream(r,e,n)}))}addUintVectorToStream(t,e,n){jo(this,e,(e=>{const r=this.i._allocateUintVector(t.length);if(!r)throw Error(\"Unable to allocate new unsigned int vector on heap.\");for(const e of t)this.i._addUintVectorEntry(r,e);this.i._addUintVectorToInputStream(r,e,n)}))}addStringVectorToStream(t,e,n){jo(this,e,(e=>{const r=this.i._allocateStringVector(t.length);if(!r)throw Error(\"Unable to allocate new string vector on heap.\");for(const e of t)jo(this,e,(t=>{this.i._addStringVectorEntry(r,t)}));this.i._addStringVectorToInputStream(r,e,n)}))}addBoolToInputSidePacket(t,e){jo(this,e,(e=>{this.i._addBoolToInputSidePacket(t,e)}))}addDoubleToInputSidePacket(t,e){jo(this,e,(e=>{this.i._addDoubleToInputSidePacket(t,e)}))}addFloatToInputSidePacket(t,e){jo(this,e,(e=>{this.i._addFloatToInputSidePacket(t,e)}))}addIntToInputSidePacket(t,e){jo(this,e,(e=>{this.i._addIntToInputSidePacket(t,e)}))}addUintToInputSidePacket(t,e){jo(this,e,(e=>{this.i._addUintToInputSidePacket(t,e)}))}addStringToInputSidePacket(t,e){jo(this,e,(e=>{jo(this,t,(t=>{this.i._addStringToInputSidePacket(t,e)}))}))}addProtoToInputSidePacket(t,e,n){jo(this,n,(n=>{jo(this,e,(e=>{const r=this.i._malloc(t.length);this.i.HEAPU8.set(t,r),this.i._addProtoToInputSidePacket(r,t.length,e,n),this.i._free(r)}))}))}addBoolVectorToInputSidePacket(t,e){jo(this,e,(e=>{const n=this.i._allocateBoolVector(t.length);if(!n)throw Error(\"Unable to allocate new bool vector on heap.\");for(const e of t)this.i._addBoolVectorEntry(n,e);this.i._addBoolVectorToInputSidePacket(n,e)}))}addDoubleVectorToInputSidePacket(t,e){jo(this,e,(e=>{const n=this.i._allocateDoubleVector(t.length);if(!n)throw Error(\"Unable to allocate new double vector on heap.\");for(const e of t)this.i._addDoubleVectorEntry(n,e);this.i._addDoubleVectorToInputSidePacket(n,e)}))}addFloatVectorToInputSidePacket(t,e){jo(this,e,(e=>{const n=this.i._allocateFloatVector(t.length);if(!n)throw Error(\"Unable to allocate new float vector on heap.\");for(const e of t)this.i._addFloatVectorEntry(n,e);this.i._addFloatVectorToInputSidePacket(n,e)}))}addIntVectorToInputSidePacket(t,e){jo(this,e,(e=>{const n=this.i._allocateIntVector(t.length);if(!n)throw Error(\"Unable to allocate new int vector on heap.\");for(const e of t)this.i._addIntVectorEntry(n,e);this.i._addIntVectorToInputSidePacket(n,e)}))}addUintVectorToInputSidePacket(t,e){jo(this,e,(e=>{const n=this.i._allocateUintVector(t.length);if(!n)throw Error(\"Unable to allocate new unsigned int vector on heap.\");for(const e of t)this.i._addUintVectorEntry(n,e);this.i._addUintVectorToInputSidePacket(n,e)}))}addStringVectorToInputSidePacket(t,e){jo(this,e,(e=>{const n=this.i._allocateStringVector(t.length);if(!n)throw Error(\"Unable to allocate new string vector on heap.\");for(const e of t)jo(this,e,(t=>{this.i._addStringVectorEntry(n,t)}));this.i._addStringVectorToInputSidePacket(n,e)}))}attachBoolListener(t,e){Ho(this,t,e),jo(this,t,(t=>{this.i._attachBoolListener(t)}))}attachBoolVectorListener(t,e){Wo(this,t,e),jo(this,t,(t=>{this.i._attachBoolVectorListener(t)}))}attachIntListener(t,e){Ho(this,t,e),jo(this,t,(t=>{this.i._attachIntListener(t)}))}attachIntVectorListener(t,e){Wo(this,t,e),jo(this,t,(t=>{this.i._attachIntVectorListener(t)}))}attachUintListener(t,e){Ho(this,t,e),jo(this,t,(t=>{this.i._attachUintListener(t)}))}attachUintVectorListener(t,e){Wo(this,t,e),jo(this,t,(t=>{this.i._attachUintVectorListener(t)}))}attachDoubleListener(t,e){Ho(this,t,e),jo(this,t,(t=>{this.i._attachDoubleListener(t)}))}attachDoubleVectorListener(t,e){Wo(this,t,e),jo(this,t,(t=>{this.i._attachDoubleVectorListener(t)}))}attachFloatListener(t,e){Ho(this,t,e),jo(this,t,(t=>{this.i._attachFloatListener(t)}))}attachFloatVectorListener(t,e){Wo(this,t,e),jo(this,t,(t=>{this.i._attachFloatVectorListener(t)}))}attachStringListener(t,e){Ho(this,t,e),jo(this,t,(t=>{this.i._attachStringListener(t)}))}attachStringVectorListener(t,e){Wo(this,t,e),jo(this,t,(t=>{this.i._attachStringVectorListener(t)}))}attachProtoListener(t,e,n){Ho(this,t,e),jo(this,t,(t=>{this.i._attachProtoListener(t,n||!1)}))}attachProtoVectorListener(t,e,n){Wo(this,t,e),jo(this,t,(t=>{this.i._attachProtoVectorListener(t,n||!1)}))}attachAudioListener(t,e,n){this.i._attachAudioListener||console.warn('Attempting to use attachAudioListener without support for output audio. Is build dep \":gl_graph_runner_audio_out\" missing?'),Ho(this,t,((t,n)=>{t=new Float32Array(t.buffer,t.byteOffset,t.length/4),e(t,n)})),jo(this,t,(t=>{this.i._attachAudioListener(t,n||!1)}))}finishProcessing(){this.i._waitUntilIdle()}closeGraph(){this.i._closeGraph(),this.i.simpleListeners=void 0,this.i.emptyPacketListeners=void 0}},class extends Wa{get ia(){return this.i}ta(t,e,n){jo(this,e,(e=>{const[r,i]=Vo(this,t,e);this.ia._addBoundTextureAsImageToStream(e,r,i,n)}))}W(t,e){Ho(this,t,e),jo(this,t,(t=>{this.ia._attachImageListener(t)}))}fa(t,e){Wo(this,t,e),jo(this,t,(t=>{this.ia._attachImageVectorListener(t)}))}}));var Wa,za=class extends Ha{};async function Ka(t,e,n){return async function(t,e,n,r){return zo(t,e,n,r)}(t,n.canvas??(Do()?void 0:document.createElement(\"canvas\")),e,n)}function Ya(t,e,n,r){if(t.V){const s=new fs;if(n?.regionOfInterest){if(!t.sa)throw Error(\"This task doesn't support region-of-interest.\");var i=n.regionOfInterest;if(i.left>=i.right||i.top>=i.bottom)throw Error(\"Expected RectF with left < right and top < bottom.\");if(i.left<0||i.top<0||i.right>1||i.bottom>1)throw Error(\"Expected RectF values to be in [0,1].\");Ln(s,1,(i.left+i.right)/2),Ln(s,2,(i.top+i.bottom)/2),Ln(s,4,i.right-i.left),Ln(s,3,i.bottom-i.top)}else Ln(s,1,.5),Ln(s,2,.5),Ln(s,4,1),Ln(s,3,1);if(n?.rotationDegrees){if(n?.rotationDegrees%90!=0)throw Error(\"Expected rotation to be a multiple of 90°.\");if(Ln(s,5,-Math.PI*n.rotationDegrees/180),n?.rotationDegrees%180!=0){const[t,r]=Go(e);n=bn(s,3)*r/t,i=bn(s,4)*t/r,Ln(s,4,n),Ln(s,3,i)}}t.g.addProtoToStream(s.g(),\"mediapipe.NormalizedRect\",t.V,r)}t.g.ta(e,t.da,r??performance.now()),t.finishProcessing()}function $a(t,e,n){if(t.baseOptions?.g())throw Error(\"Task is not initialized with image mode. 'runningMode' must be set to 'IMAGE'.\");Ya(t,e,n,t.B+1)}function qa(t,e,n,r){if(!t.baseOptions?.g())throw Error(\"Task is not initialized with video mode. 'runningMode' must be set to 'VIDEO'.\");Ya(t,e,n,r)}function Ja(t,e,n,r){var i=e.data;const s=e.width,o=s*(e=e.height);if((i instanceof Uint8Array||i instanceof Float32Array)&&i.length!==o)throw Error(\"Unsupported channel count: \"+i.length/o);return t=new wa([i],n,!1,t.g.i.canvas,t.R,s,e),r?t.clone():t}var Za=class extends Qo{constructor(t,e,n,r){super(t),this.g=t,this.da=e,this.V=n,this.sa=r,this.R=new ca}l(t,e=!0){if(\"runningMode\"in t&&Sn(this.baseOptions,2,!!t.runningMode&&\"IMAGE\"!==t.runningMode),void 0!==t.canvas&&this.g.i.canvas!==t.canvas)throw Error(\"You must create a new task to reset the canvas.\");return super.l(t,e)}close(){this.R.close(),super.close()}};Za.prototype.close=Za.prototype.close;var Qa=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect_in\",!1),this.j={detections:[]},yn(t=this.h=new Fs,0,1,e=new xs),Ln(this.h,2,.5),Ln(this.h,3,.3)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return\"minDetectionConfidence\"in t&&Ln(this.h,2,t.minDetectionConfidence??.5),\"minSuppressionThreshold\"in t&&Ln(this.h,3,t.minSuppressionThreshold??.3),this.l(t)}D(t,e){return this.j={detections:[]},$a(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},qa(this,t,n,e),this.j}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect_in\"),Yi(t,\"detections\");const e=new Ci;er(e,Is,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.face_detector.FaceDetectorGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect_in\"),ji(n,\"DETECTIONS:detections\"),n.o(e),zi(t,n),this.g.attachProtoVectorListener(\"detections\",((t,e)=>{for(const e of t)t=ss(e),this.j.detections.push(Lo(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"detections\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Qa.prototype.detectForVideo=Qa.prototype.F,Qa.prototype.detect=Qa.prototype.D,Qa.prototype.setOptions=Qa.prototype.o,Qa.createFromModelPath=async function(t,e){return Ka(Qa,t,{baseOptions:{modelAssetPath:e}})},Qa.createFromModelBuffer=function(t,e){return Ka(Qa,t,{baseOptions:{modelAssetBuffer:e}})},Qa.createFromOptions=function(t,e){return Ka(Qa,t,e)};var th=Xa([61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]),eh=Xa([263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]),nh=Xa([276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]),rh=Xa([474,475],[475,476],[476,477],[477,474]),ih=Xa([33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]),sh=Xa([46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]),oh=Xa([469,470],[470,471],[471,472],[472,469]),ah=Xa([10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]),hh=[...th,...eh,...nh,...ih,...sh,...ah],ch=Xa([127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]);function uh(t){t.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]}}var lh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!1),this.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]},this.outputFacialTransformationMatrixes=this.outputFaceBlendshapes=!1,yn(t=this.h=new Ds,0,1,e=new xs),this.v=new Us,yn(this.h,0,3,this.v),this.s=new Fs,yn(this.h,0,2,this.s),xn(this.s,4,1),Ln(this.s,2,.5),Ln(this.v,2,.5),Ln(this.h,4,.5)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return\"numFaces\"in t&&xn(this.s,4,t.numFaces??1),\"minFaceDetectionConfidence\"in t&&Ln(this.s,2,t.minFaceDetectionConfidence??.5),\"minTrackingConfidence\"in t&&Ln(this.h,4,t.minTrackingConfidence??.5),\"minFacePresenceConfidence\"in t&&Ln(this.v,2,t.minFacePresenceConfidence??.5),\"outputFaceBlendshapes\"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),\"outputFacialTransformationMatrixes\"in t&&(this.outputFacialTransformationMatrixes=!!t.outputFacialTransformationMatrixes),this.l(t)}D(t,e){return uh(this),$a(this,t,e),this.j}F(t,e,n){return uh(this),qa(this,t,n,e),this.j}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\"),Yi(t,\"face_landmarks\");const e=new Ci;er(e,js,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.face_landmarker.FaceLandmarkerGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"NORM_LANDMARKS:face_landmarks\"),n.o(e),zi(t,n),this.g.attachProtoVectorListener(\"face_landmarks\",((t,e)=>{for(const e of t)t=cs(e),this.j.faceLandmarks.push(Ro(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"face_landmarks\",(t=>{$o(this,t)})),this.outputFaceBlendshapes&&(Yi(t,\"blendshapes\"),ji(n,\"BLENDSHAPES:blendshapes\"),this.g.attachProtoVectorListener(\"blendshapes\",((t,e)=>{if(this.outputFaceBlendshapes)for(const e of t)t=es(e),this.j.faceBlendshapes.push(xo(t.g()??[]));$o(this,e)})),this.g.attachEmptyPacketListener(\"blendshapes\",(t=>{$o(this,t)}))),this.outputFacialTransformationMatrixes&&(Yi(t,\"face_geometry\"),ji(n,\"FACE_GEOMETRY:face_geometry\"),this.g.attachProtoVectorListener(\"face_geometry\",((t,e)=>{if(this.outputFacialTransformationMatrixes)for(const e of t)(t=pn(Cs(e),us,2))&&this.j.facialTransformationMatrixes.push({rows:Tn(An(t,1),0)??0,columns:Tn(An(t,2),0)??0,data:en(t,3,Ht,tn()).slice()??[]});$o(this,e)})),this.g.attachEmptyPacketListener(\"face_geometry\",(t=>{$o(this,t)}))),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};lh.prototype.detectForVideo=lh.prototype.F,lh.prototype.detect=lh.prototype.D,lh.prototype.setOptions=lh.prototype.o,lh.createFromModelPath=function(t,e){return Ka(lh,t,{baseOptions:{modelAssetPath:e}})},lh.createFromModelBuffer=function(t,e){return Ka(lh,t,{baseOptions:{modelAssetBuffer:e}})},lh.createFromOptions=function(t,e){return Ka(lh,t,e)},lh.FACE_LANDMARKS_LIPS=th,lh.FACE_LANDMARKS_LEFT_EYE=eh,lh.FACE_LANDMARKS_LEFT_EYEBROW=nh,lh.FACE_LANDMARKS_LEFT_IRIS=rh,lh.FACE_LANDMARKS_RIGHT_EYE=ih,lh.FACE_LANDMARKS_RIGHT_EYEBROW=sh,lh.FACE_LANDMARKS_RIGHT_IRIS=oh,lh.FACE_LANDMARKS_FACE_OVAL=ah,lh.FACE_LANDMARKS_CONTOURS=hh,lh.FACE_LANDMARKS_TESSELATION=ch;var fh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!0),yn(t=this.j=new Vs,0,1,e=new xs)}get baseOptions(){return pn(this.j,xs,1)}set baseOptions(t){yn(this.j,0,1,t)}o(t){return super.l(t)}Ra(t,e,n){const r=\"function\"!=typeof e?e:{};if(this.h=\"function\"==typeof e?e:n,$a(this,t,r??{}),!this.h)return this.s}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\"),Yi(t,\"stylized_image\");const e=new Ci;er(e,Xs,this.j);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.face_stylizer.FaceStylizerGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"STYLIZED_IMAGE:stylized_image\"),n.o(e),zi(t,n),this.g.W(\"stylized_image\",((t,e)=>{var n=!this.h,r=t.data,i=t.width;const s=i*(t=t.height);if(r instanceof Uint8Array)if(r.length===3*s){const e=new Uint8ClampedArray(4*s);for(let t=0;t<s;++t)e[4*t]=r[3*t],e[4*t+1]=r[3*t+1],e[4*t+2]=r[3*t+2],e[4*t+3]=255;r=new ImageData(e,i,t)}else{if(r.length!==4*s)throw Error(\"Unsupported channel count: \"+r.length/s);r=new ImageData(new Uint8ClampedArray(r.buffer,r.byteOffset,r.length),i,t)}else if(!(r instanceof WebGLTexture))throw Error(`Unsupported format: ${r.constructor.name}`);i=new ja([r],!1,!1,this.g.i.canvas,this.R,i,t),this.s=n=n?i.clone():i,this.h&&this.h(n),$o(this,e)})),this.g.attachEmptyPacketListener(\"stylized_image\",(t=>{this.s=null,this.h&&this.h(null),$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};fh.prototype.stylize=fh.prototype.Ra,fh.prototype.setOptions=fh.prototype.o,fh.createFromModelPath=function(t,e){return Ka(fh,t,{baseOptions:{modelAssetPath:e}})},fh.createFromModelBuffer=function(t,e){return Ka(fh,t,{baseOptions:{modelAssetBuffer:e}})},fh.createFromOptions=function(t,e){return Ka(fh,t,e)};var dh=Xa([0,1],[1,2],[2,3],[3,4],[0,5],[5,6],[6,7],[7,8],[5,9],[9,10],[10,11],[11,12],[9,13],[13,14],[14,15],[15,16],[13,17],[0,17],[17,18],[18,19],[19,20]);function ph(t){t.gestures=[],t.landmarks=[],t.worldLandmarks=[],t.handedness=[]}function gh(t){return 0===t.gestures.length?{gestures:[],landmarks:[],worldLandmarks:[],handedness:[],handednesses:[]}:{gestures:t.gestures,landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handedness:t.handedness,handednesses:t.handedness}}function mh(t,e=!0){const n=[];for(const i of t){var r=es(i);t=[];for(const n of r.g())r=e&&null!=An(n,1)?Tn(An(n,1),0):-1,t.push({score:bn(n,2)??0,index:r,categoryName:kn(n,3)??\"\",displayName:kn(n,4)??\"\"});n.push(t)}return n}var yh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!1),this.gestures=[],this.landmarks=[],this.worldLandmarks=[],this.handedness=[],yn(t=this.j=new Js,0,1,e=new xs),this.s=new qs,yn(this.j,0,2,this.s),this.C=new $s,yn(this.s,0,3,this.C),this.v=new Ys,yn(this.s,0,2,this.v),this.h=new Ks,yn(this.j,0,3,this.h),Ln(this.v,2,.5),Ln(this.s,4,.5),Ln(this.C,2,.5)}get baseOptions(){return pn(this.j,xs,1)}set baseOptions(t){yn(this.j,0,1,t)}o(t){if(xn(this.v,3,t.numHands??1),\"minHandDetectionConfidence\"in t&&Ln(this.v,2,t.minHandDetectionConfidence??.5),\"minTrackingConfidence\"in t&&Ln(this.s,4,t.minTrackingConfidence??.5),\"minHandPresenceConfidence\"in t&&Ln(this.C,2,t.minHandPresenceConfidence??.5),t.cannedGesturesClassifierOptions){var e=new Hs,n=e,r=So(t.cannedGesturesClassifierOptions,pn(this.h,Hs,3)?.h());yn(n,0,2,r),yn(this.h,0,3,e)}else void 0===t.cannedGesturesClassifierOptions&&pn(this.h,Hs,3)?.g();return t.customGesturesClassifierOptions?(yn(n=e=new Hs,0,2,r=So(t.customGesturesClassifierOptions,pn(this.h,Hs,4)?.h())),yn(this.h,0,4,e)):void 0===t.customGesturesClassifierOptions&&pn(this.h,Hs,4)?.g(),this.l(t)}Ma(t,e){return ph(this),$a(this,t,e),gh(this)}Na(t,e,n){return ph(this),qa(this,t,n,e),gh(this)}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\"),Yi(t,\"hand_gestures\"),Yi(t,\"hand_landmarks\"),Yi(t,\"world_hand_landmarks\"),Yi(t,\"handedness\");const e=new Ci;er(e,io,this.j);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.gesture_recognizer.GestureRecognizerGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"HAND_GESTURES:hand_gestures\"),ji(n,\"LANDMARKS:hand_landmarks\"),ji(n,\"WORLD_LANDMARKS:world_hand_landmarks\"),ji(n,\"HANDEDNESS:handedness\"),n.o(e),zi(t,n),this.g.attachProtoVectorListener(\"hand_landmarks\",((t,e)=>{for(const e of t){t=cs(e);const n=[];for(const e of mn(t,hs,1))n.push({x:bn(e,1)??0,y:bn(e,2)??0,z:bn(e,3)??0,visibility:bn(e,4)??0});this.landmarks.push(n)}$o(this,e)})),this.g.attachEmptyPacketListener(\"hand_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoVectorListener(\"world_hand_landmarks\",((t,e)=>{for(const e of t){t=as(e);const n=[];for(const e of mn(t,os,1))n.push({x:bn(e,1)??0,y:bn(e,2)??0,z:bn(e,3)??0,visibility:bn(e,4)??0});this.worldLandmarks.push(n)}$o(this,e)})),this.g.attachEmptyPacketListener(\"world_hand_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoVectorListener(\"hand_gestures\",((t,e)=>{this.gestures.push(...mh(t,!1)),$o(this,e)})),this.g.attachEmptyPacketListener(\"hand_gestures\",(t=>{$o(this,t)})),this.g.attachProtoVectorListener(\"handedness\",((t,e)=>{this.handedness.push(...mh(t)),$o(this,e)})),this.g.attachEmptyPacketListener(\"handedness\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};function _h(t){return{landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handednesses:t.handedness,handedness:t.handedness}}yh.prototype.recognizeForVideo=yh.prototype.Na,yh.prototype.recognize=yh.prototype.Ma,yh.prototype.setOptions=yh.prototype.o,yh.createFromModelPath=function(t,e){return Ka(yh,t,{baseOptions:{modelAssetPath:e}})},yh.createFromModelBuffer=function(t,e){return Ka(yh,t,{baseOptions:{modelAssetBuffer:e}})},yh.createFromOptions=function(t,e){return Ka(yh,t,e)},yh.HAND_CONNECTIONS=dh;var vh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!1),this.landmarks=[],this.worldLandmarks=[],this.handedness=[],yn(t=this.h=new qs,0,1,e=new xs),this.s=new $s,yn(this.h,0,3,this.s),this.j=new Ys,yn(this.h,0,2,this.j),xn(this.j,3,1),Ln(this.j,2,.5),Ln(this.s,2,.5),Ln(this.h,4,.5)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return\"numHands\"in t&&xn(this.j,3,t.numHands??1),\"minHandDetectionConfidence\"in t&&Ln(this.j,2,t.minHandDetectionConfidence??.5),\"minTrackingConfidence\"in t&&Ln(this.h,4,t.minTrackingConfidence??.5),\"minHandPresenceConfidence\"in t&&Ln(this.s,2,t.minHandPresenceConfidence??.5),this.l(t)}D(t,e){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],$a(this,t,e),_h(this)}F(t,e,n){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],qa(this,t,n,e),_h(this)}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\"),Yi(t,\"hand_landmarks\"),Yi(t,\"world_hand_landmarks\"),Yi(t,\"handedness\");const e=new Ci;er(e,ro,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.hand_landmarker.HandLandmarkerGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"LANDMARKS:hand_landmarks\"),ji(n,\"WORLD_LANDMARKS:world_hand_landmarks\"),ji(n,\"HANDEDNESS:handedness\"),n.o(e),zi(t,n),this.g.attachProtoVectorListener(\"hand_landmarks\",((t,e)=>{for(const e of t)t=cs(e),this.landmarks.push(Ro(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"hand_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoVectorListener(\"world_hand_landmarks\",((t,e)=>{for(const e of t)t=as(e),this.worldLandmarks.push(Fo(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"world_hand_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoVectorListener(\"handedness\",((t,e)=>{var n=this.handedness,r=n.push;const i=[];for(const e of t){t=es(e);const n=[];for(const e of t.g())n.push({score:bn(e,2)??0,index:Tn(An(e,1),0)??-1,categoryName:kn(e,3)??\"\",displayName:kn(e,4)??\"\"});i.push(n)}r.call(n,...i),$o(this,e)})),this.g.attachEmptyPacketListener(\"handedness\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};vh.prototype.detectForVideo=vh.prototype.F,vh.prototype.detect=vh.prototype.D,vh.prototype.setOptions=vh.prototype.o,vh.createFromModelPath=function(t,e){return Ka(vh,t,{baseOptions:{modelAssetPath:e}})},vh.createFromModelBuffer=function(t,e){return Ka(vh,t,{baseOptions:{modelAssetBuffer:e}})},vh.createFromOptions=function(t,e){return Ka(vh,t,e)},vh.HAND_CONNECTIONS=dh;var Eh=Xa([0,1],[1,2],[2,3],[3,7],[0,4],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[13,15],[15,17],[15,19],[15,21],[17,19],[12,14],[14,16],[16,18],[16,20],[16,22],[18,20],[11,23],[12,24],[23,24],[23,25],[24,26],[25,27],[26,28],[27,29],[28,30],[29,31],[30,32],[27,31],[28,32]);function wh(t){t.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]}}function Th(t){try{if(!t.C)return t.h;t.C(t.h)}finally{Zo(t)}}function Ah(t,e){t=cs(t),e.push(Ro(t))}var bh=class extends Za{constructor(t,e){super(new za(t,e),\"input_frames_image\",null,!1),this.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]},this.outputPoseSegmentationMasks=this.outputFaceBlendshapes=!1,yn(t=this.j=new ho,0,1,e=new xs),this.K=new $s,yn(this.j,0,2,this.K),this.ca=new so,yn(this.j,0,3,this.ca),this.s=new Fs,yn(this.j,0,4,this.s),this.I=new Us,yn(this.j,0,5,this.I),this.v=new oo,yn(this.j,0,6,this.v),this.L=new ao,yn(this.j,0,7,this.L),Ln(this.s,2,.5),Ln(this.s,3,.3),Ln(this.I,2,.5),Ln(this.v,2,.5),Ln(this.v,3,.3),Ln(this.L,2,.5),Ln(this.K,2,.5)}get baseOptions(){return pn(this.j,xs,1)}set baseOptions(t){yn(this.j,0,1,t)}o(t){return\"minFaceDetectionConfidence\"in t&&Ln(this.s,2,t.minFaceDetectionConfidence??.5),\"minFaceSuppressionThreshold\"in t&&Ln(this.s,3,t.minFaceSuppressionThreshold??.3),\"minFacePresenceConfidence\"in t&&Ln(this.I,2,t.minFacePresenceConfidence??.5),\"outputFaceBlendshapes\"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),\"minPoseDetectionConfidence\"in t&&Ln(this.v,2,t.minPoseDetectionConfidence??.5),\"minPoseSuppressionThreshold\"in t&&Ln(this.v,3,t.minPoseSuppressionThreshold??.3),\"minPosePresenceConfidence\"in t&&Ln(this.L,2,t.minPosePresenceConfidence??.5),\"outputPoseSegmentationMasks\"in t&&(this.outputPoseSegmentationMasks=!!t.outputPoseSegmentationMasks),\"minHandLandmarksConfidence\"in t&&Ln(this.K,2,t.minHandLandmarksConfidence??.5),this.l(t)}D(t,e,n){const r=\"function\"!=typeof e?e:{};return this.C=\"function\"==typeof e?e:n,wh(this),$a(this,t,r),Th(this)}F(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.C=\"function\"==typeof n?n:r,wh(this),qa(this,t,i,e),Th(this)}m(){var t=new $i;Ki(t,\"input_frames_image\"),Yi(t,\"pose_landmarks\"),Yi(t,\"pose_world_landmarks\"),Yi(t,\"face_landmarks\"),Yi(t,\"left_hand_landmarks\"),Yi(t,\"left_hand_world_landmarks\"),Yi(t,\"right_hand_landmarks\"),Yi(t,\"right_hand_world_landmarks\");const e=new Ci,n=new ki;an(n,1,ne(\"type.googleapis.com/mediapipe.tasks.vision.holistic_landmarker.proto.HolisticLandmarkerGraphOptions\"),\"\"),function(t,e){if(null!=e)if(Array.isArray(e))$e(t,2,De(e,Ge,void 0,void 0,!1));else{if(!(\"string\"==typeof e||e instanceof D||I(e)))throw Error(\"invalid value in Any.value field: \"+e+\" expected a ByteString, a base64 encoded string, a Uint8Array or a jspb array\");an(t,2,lt(e,!1,!1),N())}}(n,this.j.g());const r=new Vi;Bi(r,\"mediapipe.tasks.vision.holistic_landmarker.HolisticLandmarkerGraph\"),wn(r,8,ki,n),Gi(r,\"IMAGE:input_frames_image\"),ji(r,\"POSE_LANDMARKS:pose_landmarks\"),ji(r,\"POSE_WORLD_LANDMARKS:pose_world_landmarks\"),ji(r,\"FACE_LANDMARKS:face_landmarks\"),ji(r,\"LEFT_HAND_LANDMARKS:left_hand_landmarks\"),ji(r,\"LEFT_HAND_WORLD_LANDMARKS:left_hand_world_landmarks\"),ji(r,\"RIGHT_HAND_LANDMARKS:right_hand_landmarks\"),ji(r,\"RIGHT_HAND_WORLD_LANDMARKS:right_hand_world_landmarks\"),r.o(e),zi(t,r),qo(this,t),this.g.attachProtoListener(\"pose_landmarks\",((t,e)=>{Ah(t,this.h.poseLandmarks),$o(this,e)})),this.g.attachEmptyPacketListener(\"pose_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoListener(\"pose_world_landmarks\",((t,e)=>{var n=this.h.poseWorldLandmarks;t=as(t),n.push(Fo(t)),$o(this,e)})),this.g.attachEmptyPacketListener(\"pose_world_landmarks\",(t=>{$o(this,t)})),this.outputPoseSegmentationMasks&&(ji(r,\"POSE_SEGMENTATION_MASK:pose_segmentation_mask\"),Jo(this,\"pose_segmentation_mask\"),this.g.W(\"pose_segmentation_mask\",((t,e)=>{this.h.poseSegmentationMasks=[Ja(this,t,!0,!this.C)],$o(this,e)})),this.g.attachEmptyPacketListener(\"pose_segmentation_mask\",(t=>{this.h.poseSegmentationMasks=[],$o(this,t)}))),this.g.attachProtoListener(\"face_landmarks\",((t,e)=>{Ah(t,this.h.faceLandmarks),$o(this,e)})),this.g.attachEmptyPacketListener(\"face_landmarks\",(t=>{$o(this,t)})),this.outputFaceBlendshapes&&(Yi(t,\"extra_blendshapes\"),ji(r,\"FACE_BLENDSHAPES:extra_blendshapes\"),this.g.attachProtoListener(\"extra_blendshapes\",((t,e)=>{var n=this.h.faceBlendshapes;this.outputFaceBlendshapes&&(t=es(t),n.push(xo(t.g()??[]))),$o(this,e)})),this.g.attachEmptyPacketListener(\"extra_blendshapes\",(t=>{$o(this,t)}))),this.g.attachProtoListener(\"left_hand_landmarks\",((t,e)=>{Ah(t,this.h.leftHandLandmarks),$o(this,e)})),this.g.attachEmptyPacketListener(\"left_hand_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoListener(\"left_hand_world_landmarks\",((t,e)=>{var n=this.h.leftHandWorldLandmarks;t=as(t),n.push(Fo(t)),$o(this,e)})),this.g.attachEmptyPacketListener(\"left_hand_world_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoListener(\"right_hand_landmarks\",((t,e)=>{Ah(t,this.h.rightHandLandmarks),$o(this,e)})),this.g.attachEmptyPacketListener(\"right_hand_landmarks\",(t=>{$o(this,t)})),this.g.attachProtoListener(\"right_hand_world_landmarks\",((t,e)=>{var n=this.h.rightHandWorldLandmarks;t=as(t),n.push(Fo(t)),$o(this,e)})),this.g.attachEmptyPacketListener(\"right_hand_world_landmarks\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};bh.prototype.detectForVideo=bh.prototype.F,bh.prototype.detect=bh.prototype.D,bh.prototype.setOptions=bh.prototype.o,bh.createFromModelPath=function(t,e){return Ka(bh,t,{baseOptions:{modelAssetPath:e}})},bh.createFromModelBuffer=function(t,e){return Ka(bh,t,{baseOptions:{modelAssetBuffer:e}})},bh.createFromOptions=function(t,e){return Ka(bh,t,e)},bh.HAND_CONNECTIONS=dh,bh.POSE_CONNECTIONS=Eh,bh.FACE_LANDMARKS_LIPS=th,bh.FACE_LANDMARKS_LEFT_EYE=eh,bh.FACE_LANDMARKS_LEFT_EYEBROW=nh,bh.FACE_LANDMARKS_LEFT_IRIS=rh,bh.FACE_LANDMARKS_RIGHT_EYE=ih,bh.FACE_LANDMARKS_RIGHT_EYEBROW=sh,bh.FACE_LANDMARKS_RIGHT_IRIS=oh,bh.FACE_LANDMARKS_FACE_OVAL=ah,bh.FACE_LANDMARKS_CONTOURS=hh,bh.FACE_LANDMARKS_TESSELATION=ch;var kh=class extends Za{constructor(t,e){super(new za(t,e),\"input_image\",\"norm_rect\",!0),this.j={classifications:[]},yn(t=this.h=new lo,0,1,e=new xs)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return yn(this.h,0,2,So(t,pn(this.h,Es,2))),this.l(t)}wa(t,e){return this.j={classifications:[]},$a(this,t,e),this.j}xa(t,e,n){return this.j={classifications:[]},qa(this,t,n,e),this.j}m(){var t=new $i;Ki(t,\"input_image\"),Ki(t,\"norm_rect\"),Yi(t,\"classifications\");const e=new Ci;er(e,fo,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.image_classifier.ImageClassifierGraph\"),Gi(n,\"IMAGE:input_image\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"CLASSIFICATIONS:classifications\"),n.o(e),zi(t,n),this.g.attachProtoListener(\"classifications\",((t,e)=>{this.j=function(t){const e={classifications:mn(t,ds,1).map((t=>xo(pn(t,Qi,4)?.g()??[],Tn(An(t,2),0),kn(t,3))))};return null!=Qt(ze(t,2))&&(e.timestampMs=Tn(Qt(ze(t,2)),0)),e}(ps(t)),$o(this,e)})),this.g.attachEmptyPacketListener(\"classifications\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};kh.prototype.classifyForVideo=kh.prototype.xa,kh.prototype.classify=kh.prototype.wa,kh.prototype.setOptions=kh.prototype.o,kh.createFromModelPath=function(t,e){return Ka(kh,t,{baseOptions:{modelAssetPath:e}})},kh.createFromModelBuffer=function(t,e){return Ka(kh,t,{baseOptions:{modelAssetBuffer:e}})},kh.createFromOptions=function(t,e){return Ka(kh,t,e)};var Sh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!0),this.h=new po,this.embeddings={embeddings:[]},yn(t=this.h,0,1,e=new xs)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){var e=this.h,n=pn(this.h,Ts,2);return n=n?n.clone():new Ts,void 0!==t.l2Normalize?Sn(n,1,t.l2Normalize):\"l2Normalize\"in t&&$e(n,1),void 0!==t.quantize?Sn(n,2,t.quantize):\"quantize\"in t&&$e(n,2),yn(e,0,2,n),this.l(t)}Da(t,e){return $a(this,t,e),this.embeddings}Ea(t,e,n){return qa(this,t,n,e),this.embeddings}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\"),Yi(t,\"embeddings_out\");const e=new Ci;er(e,go,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.image_embedder.ImageEmbedderGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"EMBEDDINGS:embeddings_out\"),n.o(e),zi(t,n),this.g.attachProtoListener(\"embeddings_out\",((t,e)=>{t=vs(t),this.embeddings=function(t){return{embeddings:mn(t,ys,1).map((t=>{const e={headIndex:Tn(An(t,3),0)??-1,headName:kn(t,4)??\"\"};if(void 0!==dn(t,gs,hn(t,1)))t=en(t=pn(t,gs,hn(t,1)),1,Ht,tn()),e.floatEmbedding=t.slice();else{const n=new Uint8Array(0);e.quantizedEmbedding=pn(t,ms,hn(t,2))?.ra()?.ua()??n}return e})),timestampMs:Tn(Qt(ze(t,2)),0)}}(t),$o(this,e)})),this.g.attachEmptyPacketListener(\"embeddings_out\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Sh.cosineSimilarity=function(t,e){if(t.floatEmbedding&&e.floatEmbedding)t=Io(t.floatEmbedding,e.floatEmbedding);else{if(!t.quantizedEmbedding||!e.quantizedEmbedding)throw Error(\"Cannot compute cosine similarity between quantized and float embeddings.\");t=Io(Mo(t.quantizedEmbedding),Mo(e.quantizedEmbedding))}return t},Sh.prototype.embedForVideo=Sh.prototype.Ea,Sh.prototype.embed=Sh.prototype.Da,Sh.prototype.setOptions=Sh.prototype.o,Sh.createFromModelPath=function(t,e){return Ka(Sh,t,{baseOptions:{modelAssetPath:e}})},Sh.createFromModelBuffer=function(t,e){return Ka(Sh,t,{baseOptions:{modelAssetBuffer:e}})},Sh.createFromOptions=function(t,e){return Ka(Sh,t,e)};var xh=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){this.confidenceMasks?.forEach((t=>{t.close()})),this.categoryMask?.close()}};function Lh(t){t.categoryMask=void 0,t.confidenceMasks=void 0,t.qualityScores=void 0}function Rh(t){try{const e=new xh(t.confidenceMasks,t.categoryMask,t.qualityScores);if(!t.j)return e;t.j(e)}finally{Zo(t)}}xh.prototype.close=xh.prototype.close;var Fh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!1),this.s=[],this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new Eo,this.v=new mo,yn(this.h,0,3,this.v),yn(t=this.h,0,1,e=new xs)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return void 0!==t.displayNamesLocale?$e(this.h,2,ne(t.displayNamesLocale)):\"displayNamesLocale\"in t&&$e(this.h,2),\"outputCategoryMask\"in t&&(this.outputCategoryMask=t.outputCategoryMask??!1),\"outputConfidenceMasks\"in t&&(this.outputConfidenceMasks=t.outputConfidenceMasks??!0),super.l(t)}J(){!function(t){const e=mn(t.ga(),Vi,1).filter((t=>kn(t,1).includes(\"mediapipe.tasks.TensorsToSegmentationCalculator\")));if(t.s=[],e.length>1)throw Error(\"The graph has more than one mediapipe.tasks.TensorsToSegmentationCalculator.\");1===e.length&&(pn(e[0],Ci,7)?.l()?.g()??new Map).forEach(((e,n)=>{t.s[Number(n)]=kn(e,1)}))}(this)}ha(t,e,n){const r=\"function\"!=typeof e?e:{};return this.j=\"function\"==typeof e?e:n,Lh(this),$a(this,t,r),Rh(this)}Pa(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.j=\"function\"==typeof n?n:r,Lh(this),qa(this,t,i,e),Rh(this)}Ha(){return this.s}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\");const e=new Ci;er(e,wo,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.image_segmenter.ImageSegmenterGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),n.o(e),zi(t,n),qo(this,t),this.outputConfidenceMasks&&(Yi(t,\"confidence_masks\"),ji(n,\"CONFIDENCE_MASKS:confidence_masks\"),Jo(this,\"confidence_masks\"),this.g.fa(\"confidence_masks\",((t,e)=>{this.confidenceMasks=t.map((t=>Ja(this,t,!0,!this.j))),$o(this,e)})),this.g.attachEmptyPacketListener(\"confidence_masks\",(t=>{this.confidenceMasks=[],$o(this,t)}))),this.outputCategoryMask&&(Yi(t,\"category_mask\"),ji(n,\"CATEGORY_MASK:category_mask\"),Jo(this,\"category_mask\"),this.g.W(\"category_mask\",((t,e)=>{this.categoryMask=Ja(this,t,!1,!this.j),$o(this,e)})),this.g.attachEmptyPacketListener(\"category_mask\",(t=>{this.categoryMask=void 0,$o(this,t)}))),Yi(t,\"quality_scores\"),ji(n,\"QUALITY_SCORES:quality_scores\"),this.g.attachFloatVectorListener(\"quality_scores\",((t,e)=>{this.qualityScores=t,$o(this,e)})),this.g.attachEmptyPacketListener(\"quality_scores\",(t=>{this.categoryMask=void 0,$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Fh.prototype.getLabels=Fh.prototype.Ha,Fh.prototype.segmentForVideo=Fh.prototype.Pa,Fh.prototype.segment=Fh.prototype.ha,Fh.prototype.setOptions=Fh.prototype.o,Fh.createFromModelPath=function(t,e){return Ka(Fh,t,{baseOptions:{modelAssetPath:e}})},Fh.createFromModelBuffer=function(t,e){return Ka(Fh,t,{baseOptions:{modelAssetBuffer:e}})},Fh.createFromOptions=function(t,e){return Ka(Fh,t,e)};var Mh=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){this.confidenceMasks?.forEach((t=>{t.close()})),this.categoryMask?.close()}};Mh.prototype.close=Mh.prototype.close;var Ih=class extends nr{constructor(t){super(t)}},Ph=[0,ai,-2],Oh=[0,ti,-3,ui,ti,-1],Ch=[0,Oh],Nh=[0,Oh,ai,-1],Uh=class extends nr{constructor(t){super(t)}},Dh=[0,ti,-1,ui],Bh=class extends nr{constructor(){super()}},Gh=class extends nr{constructor(t){super(t)}},jh=[1,2,3,4,5,6,7,8,9,10,14,15],Vh=class extends nr{constructor(){super()}};Vh.prototype.g=bi([0,Qr,[0,jh,yi,Oh,yi,[0,Oh,Ph],yi,Ch,yi,[0,Ch,Ph],yi,Dh,yi,[0,ti,-3,ui,Ei],yi,[0,ti,-3,ui],yi,[0,pi,ti,-2,ui,ai,ui,-1,2,ti,Ph],yi,Nh,yi,[0,Nh,Ph],ti,Ph,pi,yi,[0,ti,-3,ui,Ph,-1],yi,[0,Qr,Dh]],pi,[0,pi,ai,-1,ui]]);var Xh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect_in\",!1),this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new Eo,this.s=new mo,yn(this.h,0,3,this.s),yn(t=this.h,0,1,e=new xs)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return\"outputCategoryMask\"in t&&(this.outputCategoryMask=t.outputCategoryMask??!1),\"outputConfidenceMasks\"in t&&(this.outputConfidenceMasks=t.outputConfidenceMasks??!0),super.l(t)}ha(t,e,n,r){const i=\"function\"!=typeof n?n:{};this.j=\"function\"==typeof n?n:r,this.qualityScores=this.categoryMask=this.confidenceMasks=void 0,n=this.B+1,r=new Vh;const s=new Gh;var o=new Ih;if(xn(o,1,255),yn(s,0,12,o),e.keypoint&&e.scribble)throw Error(\"Cannot provide both keypoint and scribble.\");if(e.keypoint){var a=new Uh;Sn(a,3,!0),Ln(a,1,e.keypoint.x),Ln(a,2,e.keypoint.y),_n(s,5,jh,a)}else{if(!e.scribble)throw Error(\"Must provide either a keypoint or a scribble.\");for(a of(o=new Bh,e.scribble))Sn(e=new Uh,3,!0),Ln(e,1,a.x),Ln(e,2,a.y),wn(o,1,Uh,e);_n(s,15,jh,o)}wn(r,1,Gh,s),this.g.addProtoToStream(r.g(),\"drishti.RenderData\",\"roi_in\",n),$a(this,t,i);t:{try{const t=new Mh(this.confidenceMasks,this.categoryMask,this.qualityScores);if(!this.j){var h=t;break t}this.j(t)}finally{Zo(this)}h=void 0}return h}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"roi_in\"),Ki(t,\"norm_rect_in\");const e=new Ci;er(e,wo,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.interactive_segmenter.InteractiveSegmenterGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"ROI:roi_in\"),Gi(n,\"NORM_RECT:norm_rect_in\"),n.o(e),zi(t,n),qo(this,t),this.outputConfidenceMasks&&(Yi(t,\"confidence_masks\"),ji(n,\"CONFIDENCE_MASKS:confidence_masks\"),Jo(this,\"confidence_masks\"),this.g.fa(\"confidence_masks\",((t,e)=>{this.confidenceMasks=t.map((t=>Ja(this,t,!0,!this.j))),$o(this,e)})),this.g.attachEmptyPacketListener(\"confidence_masks\",(t=>{this.confidenceMasks=[],$o(this,t)}))),this.outputCategoryMask&&(Yi(t,\"category_mask\"),ji(n,\"CATEGORY_MASK:category_mask\"),Jo(this,\"category_mask\"),this.g.W(\"category_mask\",((t,e)=>{this.categoryMask=Ja(this,t,!1,!this.j),$o(this,e)})),this.g.attachEmptyPacketListener(\"category_mask\",(t=>{this.categoryMask=void 0,$o(this,t)}))),Yi(t,\"quality_scores\"),ji(n,\"QUALITY_SCORES:quality_scores\"),this.g.attachFloatVectorListener(\"quality_scores\",((t,e)=>{this.qualityScores=t,$o(this,e)})),this.g.attachEmptyPacketListener(\"quality_scores\",(t=>{this.categoryMask=void 0,$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Xh.prototype.segment=Xh.prototype.ha,Xh.prototype.setOptions=Xh.prototype.o,Xh.createFromModelPath=function(t,e){return Ka(Xh,t,{baseOptions:{modelAssetPath:e}})},Xh.createFromModelBuffer=function(t,e){return Ka(Xh,t,{baseOptions:{modelAssetBuffer:e}})},Xh.createFromOptions=function(t,e){return Ka(Xh,t,e)};var Hh=class extends Za{constructor(t,e){super(new za(t,e),\"input_frame_gpu\",\"norm_rect\",!1),this.j={detections:[]},yn(t=this.h=new To,0,1,e=new xs)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return void 0!==t.displayNamesLocale?$e(this.h,2,ne(t.displayNamesLocale)):\"displayNamesLocale\"in t&&$e(this.h,2),void 0!==t.maxResults?xn(this.h,3,t.maxResults):\"maxResults\"in t&&$e(this.h,3),void 0!==t.scoreThreshold?Ln(this.h,4,t.scoreThreshold):\"scoreThreshold\"in t&&$e(this.h,4),void 0!==t.categoryAllowlist?Rn(this.h,5,t.categoryAllowlist):\"categoryAllowlist\"in t&&$e(this.h,5),void 0!==t.categoryDenylist?Rn(this.h,6,t.categoryDenylist):\"categoryDenylist\"in t&&$e(this.h,6),this.l(t)}D(t,e){return this.j={detections:[]},$a(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},qa(this,t,n,e),this.j}m(){var t=new $i;Ki(t,\"input_frame_gpu\"),Ki(t,\"norm_rect\"),Yi(t,\"detections\");const e=new Ci;er(e,Ao,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.ObjectDetectorGraph\"),Gi(n,\"IMAGE:input_frame_gpu\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"DETECTIONS:detections\"),n.o(e),zi(t,n),this.g.attachProtoVectorListener(\"detections\",((t,e)=>{for(const e of t)t=ss(e),this.j.detections.push(Lo(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"detections\",(t=>{$o(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Hh.prototype.detectForVideo=Hh.prototype.F,Hh.prototype.detect=Hh.prototype.D,Hh.prototype.setOptions=Hh.prototype.o,Hh.createFromModelPath=async function(t,e){return Ka(Hh,t,{baseOptions:{modelAssetPath:e}})},Hh.createFromModelBuffer=function(t,e){return Ka(Hh,t,{baseOptions:{modelAssetBuffer:e}})},Hh.createFromOptions=function(t,e){return Ka(Hh,t,e)};var Wh=class{constructor(t,e,n){this.landmarks=t,this.worldLandmarks=e,this.segmentationMasks=n}close(){this.segmentationMasks?.forEach((t=>{t.close()}))}};function zh(t){t.landmarks=[],t.worldLandmarks=[],t.segmentationMasks=void 0}function Kh(t){try{const e=new Wh(t.landmarks,t.worldLandmarks,t.segmentationMasks);if(!t.s)return e;t.s(e)}finally{Zo(t)}}Wh.prototype.close=Wh.prototype.close;var Yh=class extends Za{constructor(t,e){super(new za(t,e),\"image_in\",\"norm_rect\",!1),this.landmarks=[],this.worldLandmarks=[],this.outputSegmentationMasks=!1,yn(t=this.h=new bo,0,1,e=new xs),this.v=new ao,yn(this.h,0,3,this.v),this.j=new oo,yn(this.h,0,2,this.j),xn(this.j,4,1),Ln(this.j,2,.5),Ln(this.v,2,.5),Ln(this.h,4,.5)}get baseOptions(){return pn(this.h,xs,1)}set baseOptions(t){yn(this.h,0,1,t)}o(t){return\"numPoses\"in t&&xn(this.j,4,t.numPoses??1),\"minPoseDetectionConfidence\"in t&&Ln(this.j,2,t.minPoseDetectionConfidence??.5),\"minTrackingConfidence\"in t&&Ln(this.h,4,t.minTrackingConfidence??.5),\"minPosePresenceConfidence\"in t&&Ln(this.v,2,t.minPosePresenceConfidence??.5),\"outputSegmentationMasks\"in t&&(this.outputSegmentationMasks=t.outputSegmentationMasks??!1),this.l(t)}D(t,e,n){const r=\"function\"!=typeof e?e:{};return this.s=\"function\"==typeof e?e:n,zh(this),$a(this,t,r),Kh(this)}F(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.s=\"function\"==typeof n?n:r,zh(this),qa(this,t,i,e),Kh(this)}m(){var t=new $i;Ki(t,\"image_in\"),Ki(t,\"norm_rect\"),Yi(t,\"normalized_landmarks\"),Yi(t,\"world_landmarks\"),Yi(t,\"segmentation_masks\");const e=new Ci;er(e,ko,this.h);const n=new Vi;Bi(n,\"mediapipe.tasks.vision.pose_landmarker.PoseLandmarkerGraph\"),Gi(n,\"IMAGE:image_in\"),Gi(n,\"NORM_RECT:norm_rect\"),ji(n,\"NORM_LANDMARKS:normalized_landmarks\"),ji(n,\"WORLD_LANDMARKS:world_landmarks\"),n.o(e),zi(t,n),qo(this,t),this.g.attachProtoVectorListener(\"normalized_landmarks\",((t,e)=>{this.landmarks=[];for(const e of t)t=cs(e),this.landmarks.push(Ro(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"normalized_landmarks\",(t=>{this.landmarks=[],$o(this,t)})),this.g.attachProtoVectorListener(\"world_landmarks\",((t,e)=>{this.worldLandmarks=[];for(const e of t)t=as(e),this.worldLandmarks.push(Fo(t));$o(this,e)})),this.g.attachEmptyPacketListener(\"world_landmarks\",(t=>{this.worldLandmarks=[],$o(this,t)})),this.outputSegmentationMasks&&(ji(n,\"SEGMENTATION_MASK:segmentation_masks\"),Jo(this,\"segmentation_masks\"),this.g.fa(\"segmentation_masks\",((t,e)=>{this.segmentationMasks=t.map((t=>Ja(this,t,!0,!this.s))),$o(this,e)})),this.g.attachEmptyPacketListener(\"segmentation_masks\",(t=>{this.segmentationMasks=[],$o(this,t)}))),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Yh.prototype.detectForVideo=Yh.prototype.F,Yh.prototype.detect=Yh.prototype.D,Yh.prototype.setOptions=Yh.prototype.o,Yh.createFromModelPath=function(t,e){return Ka(Yh,t,{baseOptions:{modelAssetPath:e}})},Yh.createFromModelBuffer=function(t,e){return Ka(Yh,t,{baseOptions:{modelAssetBuffer:e}})},Yh.createFromOptions=function(t,e){return Ka(Yh,t,e)},Yh.POSE_CONNECTIONS=Eh;export{Ia as DrawingUtils,Qa as FaceDetector,lh as FaceLandmarker,fh as FaceStylizer,Uo as FilesetResolver,yh as GestureRecognizer,vh as HandLandmarker,bh as HolisticLandmarker,kh as ImageClassifier,Sh as ImageEmbedder,Fh as ImageSegmenter,xh as ImageSegmenterResult,Xh as InteractiveSegmenter,Mh as InteractiveSegmenterResult,ja as MPImage,wa as MPMask,Hh as ObjectDetector,Yh as PoseLandmarker,Za as VisionTaskRunner};\n//# sourceMappingURL=vision_bundle_mjs.js.map\n"], "mappings": ";;;AAAA,IAAI,IAAE,eAAa,OAAO,OAAK,OAAK,CAAC;AAAE,SAAS,EAAEA,IAAEC,IAAE;AAAC,KAAE;AAAC,aAAQC,KAAE,CAAC,eAAe,GAAEC,KAAE,GAAEC,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,UAAG,SAAOD,KAAEA,GAAED,GAAEE,EAAC,CAAC,IAAG;AAAC,QAAAF,KAAE;AAAK,cAAM;AAAA,MAAC;AAAC,IAAAA,KAAEC;AAAA,EAAC;AAAC,SAAO,SAAOH,KAAEE,MAAGA,GAAEF,EAAC,KAAGA,KAAEC;AAAC;AAAC,SAAS,IAAG;AAAC,QAAM,MAAM,cAAc;AAAC;AAAC,SAAS,EAAEI,IAAEL,IAAE;AAAC,SAAOA,KAAE,OAAO,aAAa,MAAM,MAAKA,EAAC,GAAE,QAAMK,KAAEL,KAAEK,KAAEL;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAE,IAAM,IAAE,eAAa,OAAO;AAAY,IAAI;AAAE,IAAM,IAAE,eAAa,OAAO;AAAY,SAAS,EAAEK,IAAE;AAAC,MAAG;AAAE,IAAAA,MAAG,UAAI,IAAI,gBAAa,OAAOA,EAAC;AAAA,OAAM;AAAC,QAAIJ,KAAE;AAAE,UAAMC,KAAE,IAAI,WAAW,IAAEG,GAAE,MAAM;AAAE,aAAQF,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAI;AAAC,UAAIH,KAAEK,GAAE,WAAWF,EAAC;AAAE,UAAGH,KAAE;AAAI,QAAAE,GAAED,IAAG,IAAED;AAAA,WAAM;AAAC,YAAGA,KAAE;AAAK,UAAAE,GAAED,IAAG,IAAED,MAAG,IAAE;AAAA,aAAQ;AAAC,cAAGA,MAAG,SAAOA,MAAG,OAAM;AAAC,gBAAGA,MAAG,SAAOG,KAAEE,GAAE,QAAO;AAAC,oBAAMD,KAAEC,GAAE,WAAW,EAAEF,EAAC;AAAE,kBAAGC,MAAG,SAAOA,MAAG,OAAM;AAAC,gBAAAJ,KAAE,QAAMA,KAAE,SAAOI,KAAE,QAAM,OAAMF,GAAED,IAAG,IAAED,MAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,KAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,IAAE,KAAG,KAAIE,GAAED,IAAG,IAAE,KAAGD,KAAE;AAAI;AAAA,cAAQ;AAAC,cAAAG;AAAA,YAAG;AAAC,YAAAH,KAAE;AAAA,UAAK;AAAC,UAAAE,GAAED,IAAG,IAAED,MAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,IAAE,KAAG;AAAA,QAAG;AAAC,QAAAE,GAAED,IAAG,IAAE,KAAGD,KAAE;AAAA,MAAG;AAAA,IAAC;AAAC,IAAAK,KAAEJ,OAAIC,GAAE,SAAOA,KAAEA,GAAE,SAAS,GAAED,EAAC;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAI;AAAJ,IAAM,IAAE,EAAE,WAAU,KAAE;AAAtB,IAAwB,IAAE,EAAE,WAAU,EAAE,GAAE,IAAE,CAAC;AAA7C,IAA+C,IAAE,EAAE,WAAU,KAAE;AAAE,IAAM,IAAE,EAAE;AAAU,SAAS,EAAEA,IAAE;AAAC,SAAM,CAAC,CAAC,MAAI,CAAC,CAAC,KAAG,EAAE,OAAO,KAAM,CAAC,EAAC,OAAML,GAAC,MAAIA,MAAG,MAAIA,GAAE,QAAQK,EAAC,CAAE;AAAE;AAAC,SAAS,EAAEL,IAAE;AAAC,MAAIC;AAAE,UAAOA,KAAE,EAAE,eAAaA,KAAEA,GAAE,eAAaA,KAAE,KAAI,MAAIA,GAAE,QAAQD,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,CAAC,CAAC,MAAI,CAAC,CAAC,KAAG,EAAE,OAAO,SAAO;AAAE;AAAC,SAAS,IAAG;AAAC,SAAO,EAAE,IAAE,EAAE,UAAU,KAAG,EAAE,QAAQ,KAAG,EAAE,OAAO,MAAI,EAAE,CAAC,EAAE,KAAG,EAAE,MAAM,MAAI,EAAE,MAAM;AAAC;AAAC,SAAS,EAAEK,IAAE;AAAC,SAAO,EAAE,GAAG,EAAEA,EAAC,GAAEA;AAAC;AAAC,IAAE,KAAG,EAAE,iBAAe,MAAK,EAAE,GAAG,IAAE,WAAU;AAAC;AAAE,IAAI,IAAE,CAAC,EAAE,MAAI,EAAE,SAAS,KAAG,EAAE,MAAM;AAAG,CAAC,EAAE,SAAS,KAAG,EAAE,GAAE,EAAE,GAAE,EAAE,QAAQ,MAAI,EAAE,KAAG,CAAC,EAAE,KAAG,EAAE,OAAO,KAAG,CAAC,EAAE,KAAG,EAAE,OAAO,KAAG,CAAC,EAAE,KAAG,EAAE,MAAM,MAAI,EAAE,IAAE,EAAE,gBAAgB,IAAE,EAAE,MAAM,MAAI,EAAE,KAAG,EAAE,OAAO;AAAG,IAAI,IAAE,CAAC;AAAP,IAAS,IAAE;AAAK,SAAS,EAAEA,IAAE;AAAC,MAAIL,KAAEK,GAAE,QAAOJ,KAAE,IAAED,KAAE;AAAE,EAAAC,KAAE,IAAEA,KAAE,KAAK,MAAMA,EAAC,IAAE,MAAI,KAAK,QAAQI,GAAEL,KAAE,CAAC,CAAC,MAAIC,KAAE,MAAI,KAAK,QAAQI,GAAEL,KAAE,CAAC,CAAC,IAAEC,KAAE,IAAEA,KAAE;AAAG,MAAIC,KAAE,IAAI,WAAWD,EAAC,GAAEE,KAAE;AAAE,SAAO,SAASE,IAAEL,IAAE;AAAC,aAASC,GAAED,IAAE;AAAC,aAAKE,KAAEG,GAAE,UAAQ;AAAC,YAAIJ,KAAEI,GAAE,OAAOH,IAAG,GAAEC,KAAE,EAAEF,EAAC;AAAE,YAAG,QAAME;AAAE,iBAAOA;AAAE,YAAG,CAAC,cAAc,KAAKF,EAAC;AAAE,gBAAM,MAAM,sCAAoCA,EAAC;AAAA,MAAC;AAAC,aAAOD;AAAA,IAAC;AAAC,MAAE;AAAE,aAAQE,KAAE,OAAI;AAAC,UAAIC,KAAEF,GAAE,EAAE,GAAEG,KAAEH,GAAE,CAAC,GAAEK,KAAEL,GAAE,EAAE,GAAEM,KAAEN,GAAE,EAAE;AAAE,UAAG,OAAKM,MAAG,OAAKJ;AAAE;AAAM,MAAAH,GAAEG,MAAG,IAAEC,MAAG,CAAC,GAAE,MAAIE,OAAIN,GAAEI,MAAG,IAAE,MAAIE,MAAG,CAAC,GAAE,MAAIC,MAAGP,GAAEM,MAAG,IAAE,MAAIC,EAAC;AAAA,IAAE;AAAA,EAAC,EAAEF,IAAG,SAASA,IAAE;AAAC,IAAAH,GAAEC,IAAG,IAAEE;AAAA,EAAC,CAAE,GAAEF,OAAIF,KAAEC,GAAE,SAAS,GAAEC,EAAC,IAAED;AAAC;AAAC,SAAS,IAAG;AAAC,MAAG,CAAC,GAAE;AAAC,QAAE,CAAC;AAAE,aAAQG,KAAE,iEAAiE,MAAM,EAAE,GAAEL,KAAE,CAAC,OAAM,MAAK,OAAM,OAAM,IAAI,GAAEC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAIC,KAAEG,GAAE,OAAOL,GAAEC,EAAC,EAAE,MAAM,EAAE,CAAC;AAAE,QAAEA,EAAC,IAAEC;AAAE,eAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,YAAIC,KAAEF,GAAEC,EAAC;AAAE,mBAAS,EAAEC,EAAC,MAAI,EAAEA,EAAC,IAAED;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,eAAa,OAAO;AAA1B,IAAqC,IAAE,CAAC,KAAG,cAAY,OAAO;AAAK,SAAS,EAAEE,IAAE;AAAC,MAAG,CAAC,GAAE;AAAC,QAAIL;AAAE,eAASA,OAAIA,KAAE,IAAG,EAAE,GAAEA,KAAE,EAAEA,EAAC;AAAE,QAAIC,KAAE,MAAM,KAAK,MAAMI,GAAE,SAAO,CAAC,CAAC,GAAEH,KAAEF,GAAE,EAAE,KAAG;AAAG,QAAIQ,KAAE,GAAEC,KAAE;AAAE,WAAKD,KAAEH,GAAE,SAAO,GAAEG,MAAG,GAAE;AAAC,UAAIL,KAAEE,GAAEG,EAAC,GAAEJ,KAAEC,GAAEG,KAAE,CAAC,GAAEF,KAAED,GAAEG,KAAE,CAAC,GAAED,KAAEP,GAAEG,MAAG,CAAC;AAAE,MAAAA,KAAEH,IAAG,IAAEG,OAAI,IAAEC,MAAG,CAAC,GAAEA,KAAEJ,IAAG,KAAGI,OAAI,IAAEE,MAAG,CAAC,GAAEA,KAAEN,GAAE,KAAGM,EAAC,GAAEL,GAAEQ,IAAG,IAAEF,KAAEJ,KAAEC,KAAEE;AAAA,IAAC;AAAC,YAAOC,KAAE,GAAED,KAAEJ,IAAEG,GAAE,SAAOG,IAAE;AAAA,MAAC,KAAK;AAAE,QAAAF,KAAEN,IAAG,MAAIO,KAAEF,GAAEG,KAAE,CAAC,OAAK,CAAC,KAAGN;AAAA,MAAE,KAAK;AAAE,QAAAG,KAAEA,GAAEG,EAAC,GAAEP,GAAEQ,EAAC,IAAET,GAAEK,MAAG,CAAC,IAAEL,IAAG,IAAEK,OAAI,IAAEE,MAAG,CAAC,IAAED,KAAEJ;AAAA,IAAC;AAAC,WAAOD,GAAE,KAAK,EAAE;AAAA,EAAC;AAAC,OAAID,KAAE,IAAGC,KAAE,GAAEC,KAAEG,GAAE,SAAO,OAAMJ,KAAEC;AAAG,IAAAF,MAAG,OAAO,aAAa,MAAM,MAAKK,GAAE,SAASJ,IAAEA,MAAG,KAAK,CAAC;AAAE,SAAOD,MAAG,OAAO,aAAa,MAAM,MAAKC,KAAEI,GAAE,SAASJ,EAAC,IAAEI,EAAC,GAAE,KAAKL,EAAC;AAAC;AAAC,IAAM,IAAE;AAAR,IAAiB,IAAE,EAAC,KAAI,KAAI,GAAE,KAAI,KAAI,IAAG;AAAE,SAAS,EAAEK,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG;AAAE;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAG,CAAC;AAAE,WAAO,EAAEA,EAAC;AAAE,IAAE,KAAKA,EAAC,MAAIA,KAAEA,GAAE,QAAQ,GAAE,CAAC,IAAGA,KAAE,KAAKA,EAAC;AAAE,QAAML,KAAE,IAAI,WAAWK,GAAE,MAAM;AAAE,WAAQJ,KAAE,GAAEA,KAAEI,GAAE,QAAOJ;AAAI,IAAAD,GAAEC,EAAC,IAAEI,GAAE,WAAWJ,EAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAEK,IAAE;AAAC,SAAO,KAAG,QAAMA,MAAGA,cAAa;AAAU;AAAC,IAAI,IAAE,CAAC;AAAE,IAAI;AAAE,SAAS,EAAEA,IAAE;AAAC,MAAGA,OAAI;AAAE,UAAM,MAAM,yBAAyB;AAAC;AAAC,SAAS,IAAG;AAAC,SAAO,UAAI,IAAI,EAAE,MAAK,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,IAAE,CAAC;AAAE,MAAIL,KAAEK,GAAE;AAAG,SAAO,SAAOL,KAAE,QAAMA,MAAG,EAAEA,EAAC,IAAEA,KAAE,YAAU,OAAOA,KAAE,EAAEA,EAAC,IAAE,QAAMA,KAAEK,GAAE,KAAGL;AAAC;AAAC,IAAI,IAAE,MAAK;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,QAAG,EAAEA,EAAC,GAAE,KAAK,KAAGK,IAAE,QAAMA,MAAG,MAAIA,GAAE;AAAO,YAAM,MAAM,wDAAwD;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,IAAI,WAAW,EAAE,IAAI,KAAG,CAAC;AAAA,EAAC;AAAC;AAAE,SAAS,EAAEA,IAAEL,IAAE;AAAC,EAAAK,GAAE,sCAAoCA,GAAE,oCAAkC,CAAC,IAAGA,GAAE,kCAAkC,WAASL;AAAC;AAAC,IAAI;AAAE,SAAS,IAAG;AAAC,QAAMA,KAAE,MAAM;AAAE,IAAEA,IAAE,UAAU,GAAE,SAASA,IAAE;AAAC,MAAE,WAAY,MAAI;AAAC,YAAMA;AAAA,IAAC,GAAG,CAAC;AAAA,EAAC,EAAEA,EAAC;AAAC;AAAC,SAAS,EAAEK,IAAE;AAAC,SAAO,EAAEA,KAAE,MAAMA,EAAC,GAAE,SAAS,GAAEA;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,cAAY,OAAO;AAAM;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,MAAM,UAAU,MAAM,KAAKA,EAAC;AAAC;AAAC,IAAI,IAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAM,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,IAAE,OAAO,IAAEA;AAAC;AAAC,IAAI,IAAE,EAAE;AAAR,IAAU,IAAE,EAAE,KAAK;AAAnB,IAAqB,IAAE,EAAE,KAAK;AAA9B,IAAgC,IAAE,EAAE,KAAK;AAAzC,IAA2C,IAAE,EAAE,KAAK;AAApD,IAAsD,IAAE,IAAE,CAACA,IAAEL,OAAI;AAAC,EAAAK,GAAE,CAAC,KAAGL;AAAC,IAAE,CAACK,IAAEL,OAAI;AAAC,aAASK,GAAE,IAAEA,GAAE,KAAGL,KAAE,OAAO,iBAAiBK,IAAE,EAAC,GAAE,EAAC,OAAML,IAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC,CAAC;AAAC;AAAzL,IAA2L,IAAE,IAAE,CAACK,IAAEL,OAAI;AAAC,EAAAK,GAAE,CAAC,KAAG,CAACL;AAAC,IAAE,CAACK,IAAEL,OAAI;AAAC,aAASK,GAAE,MAAIA,GAAE,KAAG,CAACL;AAAE;AAAhP,IAAkP,KAAG,IAAE,CAAAK,OAAG,IAAEA,GAAE,CAAC,IAAE,CAAAA,OAAG,IAAEA,GAAE;AAAxQ,IAA0Q,KAAG,IAAE,CAAAA,OAAGA,GAAE,CAAC,IAAE,CAAAA,OAAGA,GAAE;AAA5R,IAA8R,KAAG,IAAE,CAACA,IAAEL,OAAI;AAAC,EAAAK,GAAE,CAAC,IAAEL;AAAC,IAAE,CAACK,IAAEL,OAAI;AAAC,aAASK,GAAE,IAAEA,GAAE,IAAEL,KAAE,OAAO,iBAAiBK,IAAE,EAAC,GAAE,EAAC,OAAML,IAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC,CAAC;AAAC;AAAE,SAAS,GAAGK,IAAE;AAAC,SAAO,EAAEA,IAAE,EAAE,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,KAAGA,IAAE,UAAQ,IAAEK,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,KAAGA,IAAE,UAAQ,KAAGK,GAAE;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,CAAC;AAAX,IAAa,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAE,CAACA,MAAG,YAAU,OAAOA,MAAGA,GAAE,OAAK;AAAG;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,SAAOA,MAAG,YAAU,OAAOA,MAAG,CAAC,MAAM,QAAQA,EAAC,KAAGA,GAAE,gBAAc;AAAM;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,MAAG,QAAMI;AAAE,QAAG,YAAU,OAAOA;AAAE,MAAAA,KAAEA,KAAE,IAAI,EAAEA,IAAE,CAAC,IAAE,EAAE;AAAA,aAAUA,GAAE,gBAAc;AAAE,UAAG,EAAEA,EAAC;AAAE,QAAAA,KAAEA,GAAE,SAAO,IAAI,EAAEJ,KAAEI,KAAE,IAAI,WAAWA,EAAC,GAAE,CAAC,IAAE,EAAE;AAAA,WAAM;AAAC,YAAG,CAACL;AAAE,gBAAM,MAAM;AAAE,QAAAK,KAAE;AAAA,MAAM;AAAA;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAE,CAAC,MAAM,QAAQA,EAAC,KAAGA,GAAE,WAAS,CAAC,EAAE,IAAE,GAAGA,EAAC;AAAE;AAAC,IAAM,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,MAAG,IAAEA;AAAE,UAAM,MAAM;AAAC;AAAC,GAAG,IAAG,EAAE,GAAE,KAAG,OAAO,OAAO,EAAE;AAAE,IAAM,KAAN,MAAM,IAAE;AAAA,EAAC,YAAYA,IAAEL,IAAEC,IAAE;AAAC,SAAK,IAAE,GAAE,KAAK,IAAEI,IAAE,KAAK,IAAEL,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAG,KAAK,IAAE,KAAK,EAAE,QAAO;AAAC,YAAMI,KAAE,KAAK,EAAE,KAAK,GAAG;AAAE,aAAM,EAAC,MAAK,OAAG,OAAM,KAAK,IAAE,KAAK,EAAE,KAAK,KAAK,GAAEA,EAAC,IAAEA,GAAC;AAAA,IAAC;AAAC,WAAM,EAAC,MAAK,MAAG,OAAM,OAAM;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,IAAI,IAAG,KAAK,GAAE,KAAK,GAAE,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,IAAI;AAAG,SAAS,GAAGA,IAAEL,IAAE;AAAC,GAACA,KAAE,KAAGA,GAAE,EAAE,IAAE,YAAUK,GAAE,EAAE,IAAE,EAAEL,EAAC;AAAE;AAAC,IAAI,KAAG,OAAO,OAAO,CAAC,CAAC;AAAE,OAAO,OAAO,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,OAAO,CAAC,CAAC;AAAE,SAAS,GAAGK,IAAE;AAAC,SAAOA,GAAE,KAAG,MAAGA;AAAC;AAAC,IAAI,KAAG,GAAI,CAAAA,OAAG,YAAU,OAAOA,EAAE;AAAjC,IAAmC,KAAG,GAAI,CAAAA,OAAG,YAAU,OAAOA,EAAE;AAAhE,IAAkE,KAAG,GAAI,CAAAA,OAAG,aAAW,OAAOA,EAAE;AAAhG,IAAkG,KAAG,cAAY,OAAO,EAAE,UAAQ,YAAU,OAAO,EAAE,OAAO,CAAC;AAA7J,IAA+J,KAAG,GAAI,CAAAA,OAAG,KAAGA,MAAG,MAAIA,MAAG,KAAG,QAAMA,GAAE,CAAC,IAAE,GAAGA,IAAE,EAAE,IAAE,GAAGA,IAAE,EAAE,CAAE;AAAE,IAAM,KAAG,OAAO,iBAAiB,SAAS;AAA1C,IAA4C,KAAG,KAAG,OAAO,OAAO,gBAAgB,IAAE;AAAlF,IAAyF,KAAG,OAAO,iBAAiB,SAAS;AAA7H,IAA+H,KAAG,KAAG,OAAO,OAAO,gBAAgB,IAAE;AAAO,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAGK,GAAE,SAAOL,GAAE;AAAO,WAAM;AAAG,MAAGK,GAAE,SAAOL,GAAE,UAAQK,OAAIL;AAAE,WAAM;AAAG,WAAQC,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,MAAI;AAAC,UAAMC,KAAEG,GAAEJ,EAAC,GAAEE,KAAEH,GAAEC,EAAC;AAAE,QAAGC,KAAEC;AAAE,aAAM;AAAG,QAAGD,KAAEC;AAAE,aAAM;AAAA,EAAE;AAAC;AAAC,IAAM,KAAG,cAAY,OAAO,WAAW,UAAU;AAAM,IAAI;AAAJ,IAAO,KAAG;AAAV,IAAY,KAAG;AAAE,SAAS,GAAGE,IAAE;AAAC,QAAML,KAAEK,OAAI;AAAE,OAAGL,IAAE,MAAIK,KAAEL,MAAG,eAAa;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAGA,KAAE,GAAE;AAAC,OAAG,CAACA,EAAC;AAAE,UAAK,CAACL,IAAEC,EAAC,IAAE,GAAG,IAAG,EAAE;AAAE,SAAGD,OAAI,GAAE,KAAGC,OAAI;AAAA,EAAC;AAAM,OAAGI,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAML,KAAE,YAAK,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;AAAE,EAAAA,GAAE,WAAW,GAAE,CAACK,IAAE,IAAE,GAAE,KAAG,GAAE,KAAGL,GAAE,UAAU,GAAE,IAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,aAAWA,MAAGK,OAAI;AAAE;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,QAAMC,KAAE,aAAWD;AAAE,SAAOC,OAAID,KAAE,CAACA,OAAI,GAAE,MAAIK,KAAE,IAAE,CAACA,OAAI,OAAKL,KAAEA,KAAE,MAAI,KAAIK,KAAE,GAAGA,IAAEL,EAAC,GAAEC,KAAE,CAACI,KAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAGK,QAAK,IAAGL,QAAK,MAAI;AAAQ,QAAIC,KAAE,MAAI,aAAWD,KAAEK;AAAA;AAAQ,MAAE,IAAEJ,KAAE,MAAI,OAAOD,EAAC,KAAG,OAAO,EAAE,IAAE,OAAOK,EAAC,MAAIA,MAAG,WAASA,MAAG,WAASJ,KAAE,YAAUI,OAAI,KAAGL,MAAG,MAAI,WAASA,KAAEA,MAAG,KAAG,QAAOC,MAAG,UAAQD,IAAEA,MAAG,GAAEK,MAAG,QAAMJ,MAAGI,KAAE,QAAM,GAAEA,MAAG,MAAKJ,MAAG,QAAMD,MAAGC,KAAE,QAAM,GAAEA,MAAG,MAAKA,KAAED,KAAE,GAAGC,EAAC,IAAE,GAAGI,EAAC;AAAG,SAAOJ;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,SAAOA,KAAE,OAAOA,EAAC,GAAE,UAAU,MAAMA,GAAE,MAAM,IAAEA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAGA,GAAE,SAAO;AAAG,OAAG,OAAOA,EAAC,CAAC;AAAA,WAAU,EAAE;AAAE,IAAAA,KAAE,OAAOA,EAAC,GAAE,KAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,MAAI,GAAE,KAAG,OAAOA,MAAG,OAAO,EAAE,IAAE,OAAO,UAAU,CAAC;AAAA,OAAM;AAAC,UAAML,KAAE,EAAE,QAAMK,GAAE,CAAC;AAAG,SAAG,KAAG;AAAE,UAAMJ,KAAEI,GAAE;AAAO,aAAQH,KAAEF,IAAEG,MAAGF,KAAED,MAAG,IAAEA,IAAEG,MAAGF,IAAEC,KAAEC,IAAEA,MAAG,GAAE;AAAC,YAAMH,KAAE,OAAOK,GAAE,MAAMH,IAAEC,EAAC,CAAC;AAAE,YAAI,KAAI,KAAG,MAAI,KAAGH,IAAE,MAAI,eAAa,MAAI,KAAK,MAAM,KAAG,UAAU,GAAE,QAAM,GAAE,QAAM;AAAA,IAAE;AAAC,QAAGA,IAAE;AAAC,YAAK,CAACK,IAAEL,EAAC,IAAE,GAAG,IAAG,EAAE;AAAE,WAAGK,IAAE,KAAGL;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAOA,KAAE,CAACA,IAAEK,KAAEA,KAAE,IAAE,CAACA,KAAEL,MAAG,GAAE,CAACK,IAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAE,UAAQA,MAAG,eAAaA,MAAG,gBAAcA,KAAE,OAAOA,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,aAAW,OAAOA,KAAEA,KAAE,YAAU,OAAOA,KAAE,CAAC,CAACA,KAAE;AAAM;AAAC,IAAM,KAAG;AAAiC,SAAS,GAAGA,IAAE;AAAC,QAAML,KAAE,OAAOK;AAAE,UAAOL,IAAE;AAAA,IAAC,KAAI;AAAS,aAAM;AAAA,IAAG,KAAI;AAAS,aAAO,OAAO,SAASK,EAAC;AAAA,EAAC;AAAC,SAAM,aAAWL,MAAG,GAAG,KAAKK,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA;AAAE,WAAOA;AAAE,MAAG,YAAU,OAAOA,IAAE;AAAC,QAAG,CAACA;AAAE;AAAO,IAAAA,KAAE,CAACA;AAAA,EAAC;AAAC,SAAM,YAAU,OAAOA,MAAG,OAAO,SAASA,EAAC,IAAE,IAAEA,KAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA;AAAE,WAAOA;AAAE,MAAG,YAAU,OAAOA,IAAE;AAAC,QAAG,CAACA;AAAE;AAAO,IAAAA,KAAE,CAACA;AAAA,EAAC;AAAC,SAAM,YAAU,OAAOA,MAAG,OAAO,SAASA,EAAC,IAAEA,OAAI,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,QAAMA,GAAE,CAAC,MAAIA,GAAE,SAAO,MAAI,OAAKA,GAAE,UAAQ,OAAOA,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE;AAAO;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,KAAK,MAAMA,EAAC,GAAE,OAAO,cAAcA,EAAC,MAAI,GAAGA,EAAC,GAAEA,KAAE,GAAG,IAAG,EAAE,IAAGA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIL,KAAE,KAAK,MAAM,OAAOK,EAAC,CAAC;AAAE,MAAG,OAAO,cAAcL,EAAC;AAAE,WAAO,OAAOA,EAAC;AAAE,MAAG,QAAMA,KAAEK,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEL,EAAC,IAAG,EAAE,QAAMK,GAAE,CAAC,IAAEA,GAAE,SAAO,MAAI,OAAKA,GAAE,UAAQ,OAAOA,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE,UAAQA,GAAE,SAAO,MAAI,OAAKA,GAAE,UAAQ,OAAOA,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE;AAAQ,QAAG,GAAGA,EAAC,GAAEA,KAAE,IAAG,cAAYL,KAAE;AAAI,UAAG,EAAE;AAAE,QAAAK,KAAE,MAAI,OAAO,IAAEL,EAAC,KAAG,OAAO,EAAE,IAAE,OAAOK,OAAI,CAAC;AAAA,WAAO;AAAC,cAAK,CAACJ,IAAEC,EAAC,IAAE,GAAGG,IAAEL,EAAC;AAAE,QAAAK,KAAE,MAAI,GAAGJ,IAAEC,EAAC;AAAA,MAAC;AAAA;AAAM,MAAAG,KAAE,GAAGA,IAAEL,EAAC;AAAE,SAAOK;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,KAAEA,KAAE,YAAU,OAAOA,MAAG,GAAGA,EAAC,IAAEA,KAAE,OAAOA,EAAC,KAAGA,KAAE,OAAO,OAAO,IAAGA,EAAC,GAAEA,KAAE,GAAGA,EAAC,IAAE,OAAOA,EAAC,IAAE,OAAOA,EAAC,IAAGA,MAAG,GAAGA,EAAC,IAAE,YAAU,OAAOA,KAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA;AAAE,WAAOA;AAAE,MAAIL,KAAE,OAAOK;AAAE,MAAG,aAAWL;AAAE,WAAO,OAAO,OAAO,QAAQ,IAAGK,EAAC,CAAC;AAAE,MAAG,GAAGA,EAAC,GAAE;AAAC,QAAG,aAAWL;AAAE,aAAOA,KAAE,KAAK,MAAM,OAAOK,EAAC,CAAC,GAAE,OAAO,cAAcL,EAAC,KAAGA,MAAG,IAAEK,KAAE,OAAOL,EAAC,KAAG,QAAMA,KAAEK,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEL,EAAC,IAAG,GAAGK,EAAC,MAAI,GAAGA,EAAC,GAAEA,KAAE,GAAG,IAAG,EAAE,KAAIA;AAAE,QAAG,aAAWL;AAAE,cAAOK,KAAE,KAAK,MAAMA,EAAC,MAAI,KAAG,OAAO,cAAcA,EAAC,IAAEA,KAAE,SAASA,IAAE;AAAC,YAAGA,KAAE,GAAE;AAAC,aAAGA,EAAC;AAAE,gBAAML,KAAE,GAAG,IAAG,EAAE;AAAE,iBAAOK,KAAE,OAAOL,EAAC,GAAE,OAAO,cAAcK,EAAC,IAAEA,KAAEL;AAAA,QAAC;AAAC,eAAO,GAAG,OAAOK,EAAC,CAAC,IAAEA,MAAG,GAAGA,EAAC,GAAE,GAAG,IAAG,EAAE;AAAA,MAAE,EAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,YAAU,OAAOA;AAAE,UAAM,MAAM;AAAE,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,MAAG,YAAU,OAAOA;AAAE,UAAM,MAAM;AAAE,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAE;AAAM;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMG,MAAG,YAAU,OAAOA,MAAGA,GAAE,MAAI;AAAG,WAAOA;AAAE,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,WAAOJ,KAAE,IAAEC,MAAGG,KAAEL,GAAE,CAAC,KAAGA,KAAEK,MAAG,IAAIA,KAAE,IAAIL,MAAG,CAAC,GAAEA,KAAEA,GAAE,CAAC,IAAEK,MAAGL,KAAE,IAAIA,OAAEA,KAAE,QAAOA;AAAE,MAAIG,KAAEF,KAAE,GAAGI,EAAC;AAAE,SAAO,MAAIF,OAAIA,MAAG,KAAGD,KAAGC,MAAG,IAAED,IAAEC,OAAIF,MAAG,GAAGI,IAAEF,EAAC,GAAE,IAAIH,GAAEK,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,MAAGD;AAAE,OAAE;AAAC,UAAG,CAAC,GAAGA,KAAEK,EAAC;AAAE,cAAM,EAAE,OAAO;AAAE,cAAO,OAAOL,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAA,KAAE,GAAGA,EAAC;AAAE,gBAAM;AAAA,QAAE,KAAI;AAAS,cAAGK,KAAEL,KAAE,OAAO,OAAO,IAAGA,EAAC,GAAE,GAAGK,EAAC,GAAE;AAAC,gBAAG,CAAC,4BAA4B,KAAKA,EAAC;AAAE,oBAAM,MAAM,OAAOA,EAAC,CAAC;AAAA,UAAC,WAAS,GAAGA,EAAC,KAAG,CAAC,OAAO,cAAcA,EAAC;AAAE,kBAAM,MAAM,OAAOA,EAAC,CAAC;AAAE,UAAAL,KAAE,KAAG,OAAOA,EAAC,IAAE,GAAGA,EAAC,IAAEA,KAAE,MAAI,MAAI,GAAGA,EAAC,IAAEA,GAAE,KAAK,KAAG,MAAI,OAAOA,EAAC;AAAE,gBAAM;AAAA,QAAE;AAAQ,UAAAA,KAAE,GAAGA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA;AAAM,IAAAA,KAAE,GAAGK,EAAC;AAAE,SAAM,YAAU,QAAOJ,KAAE,SAAOI,KAAEL,MAAGC,KAAE,IAAE,SAAOI,QAAKL,KAAE,CAACC,IAAE,OAAO,cAAcD,EAAC,KAAGA,KAAEC;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,MAAG,WAAS,OAAK,KAAG,cAAY,OAAO,QAAM,GAAG,KAAK,IAAE,OAAM,CAAC,MAAI,CAAC,GAAG;AAAE,WAAOA;AAAE,MAAIL,KAAE,yBAAI,IAAIK;AAAG,SAAOL,OAAI,KAAK,OAAO,IAAE,OAAIK,MAAG,SAASA,IAAE;AAAC,QAAG,WAAS,IAAG;AAAC,YAAMA,KAAE,IAAI,GAAG,CAAC,GAAE,CAAC,CAAC;AAAE,WAAG,MAAI,MAAM,UAAU,OAAO,KAAK,CAAC,GAAEA,EAAC,EAAE;AAAA,IAAM;AAAC,UAAI,cAAY,OAAO,UAAQ,OAAO,uBAAqBA,GAAE,OAAO,kBAAkB,IAAE;AAAA,EAAG,EAAEA,EAAC,GAAEL,KAAE,IAAI,GAAGK,IAAE,EAAC,KAAI,CAACA,IAAEL,IAAEC,QAAK,EAAE,GAAEI,GAAEL,EAAC,IAAEC,IAAE,MAAG,CAAC,GAAE,SAASI,IAAEL,IAAE;AAAC,KAAC,YAAK,IAAI,OAAI,IAAIK,IAAEL,EAAC,IAAG,YAAK,IAAI,OAAI,IAAIA,IAAEK,EAAC;AAAA,EAAC,EAAEA,IAAEL,EAAC,GAAEA;AAAG;AAAC,IAAI;AAAJ,IAAO;AAAP,IAAU;AAAV,IAAa;AAAb,IAAgB;AAAhB,IAAmB;AAAnB,IAAsB;AAAtB,IAAyB;AAAzB,IAA4B;AAAG,SAAS,KAAI;AAAC,SAAO,WAAS,OAAK,KAAG,cAAY,OAAO,UAAQ,GAAG,OAAO,IAAE,OAAM;AAAE;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAG;AAAC,WAAM,OAAKA,GAAE,SAAS,EAAE,QAAQ,eAAe,IAAEA,KAAE;AAAA,EAAI,QAAM;AAAC,WAAO;AAAA,EAAI;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAzpV,MAAAS;AAA0pV,MAAG,KAAG,GAAG,GAAE;AAAC,SAAGA,MAAA,yBAAI,IAAIV,QAAR,gBAAAU,IAAY,IAAIL,KAAG;AAAC,UAAGJ;AAAE;AAAA,IAAM,WAAS,KAAK,OAAO,IAAE;AAAI;AAAO,QAAIC,KAAEG,GAAE;AAAO,IAAAJ,KAAE,EAAC,QAAOC,GAAC;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,IAAID,IAAE,EAAE,GAAEC,MAAI;AAAC,UAAGD,MAAG;AAAG,YAAIE,KAAED;AAAA,WAAM;AAAC,QAAAC,KAAEF,KAAE;AAAG,cAAMG,KAAE,KAAK,MAAMF,KAAEC,EAAC;AAAE,QAAAA,KAAEC,KAAE,KAAK,MAAM,KAAK,OAAO,KAAG,KAAK,OAAOF,KAAE,KAAGC,EAAC,IAAEC,GAAE;AAAA,MAAC;AAAC,MAAAJ,GAAEG,EAAC,IAAEC,GAAED,EAAC;AAAA,IAAC;AAAC,OAAGC,IAAEJ,EAAC,MAAIE,MAAGD,KAAE,YAAK,IAAI,OAAI,IAAIF,EAAC,OAAKG,KAAE,IAAI,MAAGD,GAAE,IAAIF,IAAEG,EAAC,IAAGA,GAAE,IAAIE,IAAEJ,EAAC,MAAI,EAAE,GAAE,GAAGI,IAAEL,EAAC;AAAA,EAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAngW,MAAAU;AAAogW,QAAMT,MAAES,MAAA,yBAAI,IAAIV,QAAR,gBAAAU,IAAY,IAAIL;AAAG,EAAAJ,MAAG,CAAC,GAAGI,IAAEJ,EAAC,MAAI,EAAE,GAAE,GAAGI,IAAEL,EAAC;AAAE;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,MAAGK,GAAE,WAASL,GAAE;AAAO,WAAM;AAAG,aAAUG,MAAKH,IAAE;AAAC,QAAIC,IAAEC,KAAE,OAAOC,EAAC;AAAE,SAAIF,KAAE,OAAO,UAAUC,EAAC,OAAKD,KAAEI,GAAEH,EAAC,GAAEA,KAAEF,GAAEE,EAAC,GAAED,KAAE,EAAE,OAAO,MAAMA,EAAC,IAAE,OAAO,MAAMC,EAAC,IAAED,OAAIC,MAAID;AAAE,aAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAC,SAAS,GAAGI,IAAE;AAAC,MAAGA,OAAG,yBAAI,IAAIA,MAAG;AAAC,QAAIL,KAAEK,GAAE;AAAE,QAAGL;AAAE,eAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAMC,KAAEF,GAAEC,EAAC;AAAE,YAAGA,OAAID,GAAE,SAAO,KAAG,GAAGE,EAAC;AAAE,qBAAUF,MAAKE,IAAE;AAAC,kBAAMD,KAAEC,GAAEF,EAAC;AAAE,kBAAM,QAAQC,EAAC,KAAG,GAAGA,IAAEI,EAAC;AAAA,UAAC;AAAA;AAAM,gBAAM,QAAQH,EAAC,KAAG,GAAGA,IAAEG,EAAC;AAAA,MAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAt9W,MAAAU;AAAu9W,GAAAA,MAAA,yBAAI,IAAIV,QAAR,gBAAAU,IAAY,OAAOL;AAAE;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,SAAOK,KAAE,GAAGA,IAAEL,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,EAAEK,IAAE,KAAK,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,MAAG,QAAMI,OAAIA,KAAE,KAAI,KAAG,QAAO,QAAMA,IAAE;AAAC,QAAIH,KAAE;AAAG,IAAAD,MAAGI,KAAE,CAACJ,EAAC,GAAEC,MAAG,OAAKG,KAAE,CAAC,GAAEL,OAAIE,KAAE,YAAUA,MAAG,OAAKF,OAAI;AAAA,EAAG,OAAK;AAAC,QAAG,CAAC,MAAM,QAAQK,EAAC;AAAE,YAAM,MAAM,MAAM;AAAE,QAAG,QAAMH,KAAE,GAAGG,EAAC;AAAG,YAAM,MAAM,MAAM;AAAE,QAAG,KAAGH;AAAE,aAAOG;AAAE,QAAGH,MAAG,IAAGD,OAAIC,MAAG,KAAID,OAAII,GAAE,CAAC;AAAG,YAAM,MAAM,KAAK;AAAE,OAAE;AAAC,YAAMF,MAAGF,KAAEI,IAAG;AAAO,UAAGF,IAAE;AAAC,cAAME,KAAEF,KAAE;AAAE,YAAG,GAAGF,GAAEI,EAAC,CAAC,GAAE;AAAC,eAAIL,KAAEK,MAAG,CAAC,CAAC,EAAE,OAAKH,MAAG,QAAM,OAAK;AAAK,kBAAM,MAAM,QAAQ;AAAE,UAAAA,KAAE,YAAUA,MAAG,OAAKF,OAAI;AAAG,gBAAM;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGA,IAAE;AAAC,aAAIA,KAAE,KAAK,IAAIA,IAAEG,MAAG,CAAC,CAAC,EAAE,MAAID,MAAG,EAAE,KAAG;AAAK,gBAAM,MAAM,MAAM;AAAE,QAAAA,KAAE,YAAUA,MAAG,OAAKF,OAAI;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,GAAGK,IAAEH,EAAC,GAAEG;AAAC;AAAC,IAAM,KAAG,CAAC;AAAE,IAAI,KAAG,WAAU;AAAC,MAAG;AAAC,WAAO,EAAE,IAAI,cAAc,IAAG;AAAA,MAAC,cAAa;AAAC,cAAM;AAAA,MAAC;AAAA,IAAC,GAAC,GAAE;AAAA,EAAE,QAAM;AAAC,WAAM;AAAA,EAAE;AAAC,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,cAAa;AAAC,SAAK,IAAE,oBAAI;AAAA,EAAG;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAEL,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIK,IAAEL,EAAC,GAAE,KAAK,OAAK,KAAK,EAAE,MAAK;AAAA,EAAI;AAAA,EAAC,OAAOK,IAAE;AAAC,WAAOA,KAAE,KAAK,EAAE,OAAOA,EAAC,GAAE,KAAK,OAAK,KAAK,EAAE,MAAKA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,MAAM,GAAE,KAAK,OAAK,KAAK,EAAE;AAAA,EAAI;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,EAAE,KAAK;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,EAAE,OAAO;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEL,IAAE;AAAC,WAAO,KAAK,EAAE,QAAQK,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG,MAAI,OAAO,eAAe,GAAG,WAAU,IAAI,SAAS,GAAE,OAAO,iBAAiB,GAAG,WAAU,EAAC,MAAK,EAAC,OAAM,GAAE,cAAa,MAAG,YAAW,MAAG,UAAS,KAAE,EAAC,CAAC,GAAE,MAAI,cAAc,IAAG;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGK,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,IAAEA,GAAE;AAAE,UAAM,MAAM,gCAAgC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAEC,KAAE,IAAGC,KAAE,IAAG;AAAC,UAAM;AAAE,QAAIC,KAAE,GAAGE,EAAC;AAAE,IAAAF,MAAG,IAAG,GAAGE,IAAEF,EAAC,GAAE,KAAK,IAAEA,IAAE,KAAK,IAAEH,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAG,KAAK,IAAE,KAAGC;AAAE,aAAQE,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,YAAME,KAAED,GAAED,EAAC,GAAEG,KAAEN,GAAEK,GAAE,CAAC,GAAE,OAAG,IAAE;AAAE,UAAIE,KAAEF,GAAE,CAAC;AAAE,MAAAN,KAAE,WAASQ,OAAIA,KAAE,QAAMA,KAAEN,GAAEI,GAAE,CAAC,GAAE,OAAG,MAAG,QAAO,QAAOH,EAAC,GAAE,MAAM,IAAII,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGH,KAAE,IAAG;AAAC,QAAG,MAAI,KAAK;AAAK,aAAO,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,KAAE,IAAG;AAAC,UAAML,KAAE,CAAC,GAAEC,KAAE,MAAM,QAAQ;AAAE,aAAQC,IAAE,EAAEA,KAAED,GAAE,KAAK,GAAG;AAAM,OAACC,KAAEA,GAAE,OAAO,CAAC,IAAEG,GAAEH,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAEG,GAAEH,GAAE,CAAC,CAAC,GAAEF,GAAE,KAAKE,EAAC;AAAE,WAAOF;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,OAAG,IAAI,GAAE,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,OAAOK,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,MAAM,OAAO,KAAK,EAAEA,IAAE,MAAG,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAIA,KAAE,KAAK,GAAG;AAAE,WAAO,IAAI,GAAGA,IAAE,IAAG,IAAI;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,GAAG;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAIA,KAAE,KAAK,GAAG;AAAE,WAAO,IAAI,GAAGA,IAAE,GAAG,UAAU,KAAI,IAAI;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEL,IAAE;AAAC,UAAM,QAAS,CAACC,IAAEC,OAAI;AAAC,MAAAG,GAAE,KAAKL,IAAE,KAAK,IAAIE,EAAC,GAAEA,IAAE,IAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAIG,IAAEL,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,SAAOK,KAAE,KAAK,EAAEA,IAAE,MAAG,KAAE,KAAG,OAAK,QAAML,MAAG,MAAM,OAAOK,EAAC,GAAE,QAAM,MAAM,IAAIA,IAAE,KAAK,GAAGL,IAAE,MAAG,MAAG,KAAK,GAAE,OAAG,KAAK,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,GAAGK,IAAE;AAAC,UAAML,KAAE,KAAK,EAAEK,GAAE,CAAC,GAAE,OAAG,IAAE;AAAE,IAAAA,KAAEA,GAAE,CAAC,GAAEA,KAAE,KAAK,IAAE,WAASA,KAAE,OAAKA,KAAE,KAAK,GAAGA,IAAE,OAAG,MAAG,QAAO,OAAG,KAAK,CAAC,GAAE,MAAM,IAAIL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,MAAM,IAAI,KAAK,EAAEA,IAAE,OAAG,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,IAAAA,KAAE,KAAK,EAAEA,IAAE,OAAG,KAAE;AAAE,UAAML,KAAE,MAAM,IAAIK,EAAC;AAAE,QAAG,WAASL,IAAE;AAAC,UAAIC,KAAE,KAAK;AAAE,aAAOA,OAAIA,KAAE,KAAK,GAAGD,IAAE,OAAG,MAAGC,IAAE,KAAK,IAAG,KAAK,CAAC,OAAKD,MAAG,MAAM,IAAIK,IAAEJ,EAAC,GAAEA,MAAGD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,MAAM,KAAK,MAAM,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,MAAM,KAAK;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOC,KAAE,GAAGA,IAAEH,IAAED,IAAEG,EAAC,GAAED,OAAIE,KAAE,GAAGA,EAAC,IAAGA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,CAACA,IAAE,KAAK,IAAIA,EAAC,CAAC;AAAC;AAAC,IAAI;AAAG,SAAS,KAAI;AAAC,SAAO,YAAK,IAAI,GAAG,GAAG,CAAC,CAAC,GAAE,QAAO,QAAO,QAAO,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAME,IAAE;AAAC,QAAG,MAAM,QAAQA,EAAC;AAAE,MAAAA,KAAE,GAAGA,EAAC,IAAE,SAAOF,MAAG,IAAE,GAAGE,EAAC,IAAEA,KAAE,GAAGA,IAAEL,IAAEC,IAAE,WAASC,IAAEC,EAAC;AAAA,aAAU,GAAGE,EAAC,GAAE;AAAC,YAAMD,KAAE,CAAC;AAAE,eAAQE,MAAKD;AAAE,QAAAD,GAAEE,EAAC,IAAE,GAAGD,GAAEC,EAAC,GAAEN,IAAEC,IAAEC,IAAEC,EAAC;AAAE,MAAAE,KAAED;AAAA,IAAC;AAAM,MAAAC,KAAEL,GAAEK,IAAEH,EAAC;AAAE,WAAOG;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,MAAGD,KAAE,GAAGI,EAAC,IAAE;AAAE,EAAAH,KAAEA,KAAE,CAAC,EAAE,KAAGE,MAAG;AAAO,QAAME,KAAE,EAAED,EAAC;AAAE,WAAQA,KAAE,GAAEA,KAAEC,GAAE,QAAOD;AAAI,IAAAC,GAAED,EAAC,IAAE,GAAGC,GAAED,EAAC,GAAEL,IAAEC,IAAEC,IAAEC,EAAC;AAAE,SAAOF,OAAI,GAAGK,IAAED,EAAC,GAAEJ,GAAEG,IAAEE,EAAC,IAAGA;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,GAAGA,IAAE,IAAG,QAAO,QAAO,KAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAI,KAAGA,GAAE,OAAO,IAAEA,cAAa,KAAGA,GAAE,GAAG,EAAE,IAAE,SAASA,IAAE;AAAC,YAAO,OAAOA,IAAE;AAAA,MAAC,KAAI;AAAS,eAAO,SAASA,EAAC,IAAEA,KAAE,OAAOA,EAAC;AAAA,MAAE,KAAI;AAAS,eAAO,GAAGA,EAAC,IAAE,OAAOA,EAAC,IAAE,OAAOA,EAAC;AAAA,MAAE,KAAI;AAAU,eAAOA,KAAE,IAAE;AAAA,MAAE,KAAI;AAAS,YAAGA;AAAE,cAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,gBAAG,GAAGA,EAAC;AAAE;AAAA,UAAM,OAAK;AAAC,gBAAG,EAAEA,EAAC;AAAE,qBAAO,EAAEA,EAAC;AAAE,gBAAGA,cAAa,GAAE;AAAC,oBAAML,KAAEK,GAAE;AAAG,qBAAO,QAAML,KAAE,KAAG,YAAU,OAAOA,KAAEA,KAAEK,GAAE,KAAG,EAAEL,EAAC;AAAA,YAAC;AAAC,gBAAGK,cAAa;AAAG,qBAAOA,GAAE,GAAG;AAAA,UAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC,EAAEA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,KAAE,IAAG;AAAC,MAAG,QAAMI,IAAE;AAAC,QAAG,KAAGA,cAAa;AAAW,aAAOL,KAAEK,KAAE,IAAI,WAAWA,EAAC;AAAE,QAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,UAAIH,KAAE,GAAGG,EAAC;AAAE,aAAO,IAAEH,KAAEG,MAAGL,YAAI,MAAIE,MAAG,CAAC,EAAE,KAAGA,OAAI,EAAE,KAAGA,MAAG,EAAE,KAAGA,OAAIF,MAAG,GAAGK,IAAE,UAAQ,KAAGH,GAAE,GAAEG,MAAG,GAAGA,IAAE,IAAG,IAAEH,KAAE,KAAGD,IAAE,MAAG,IAAE;AAAA,IAAE;AAAC,WAAOI,GAAE,MAAI,MAAIJ,KAAEI,GAAE,GAAEA,KAAE,KAAGH,KAAE,GAAGD,EAAC,KAAGI,KAAE,GAAGA,IAAEJ,IAAEC,IAAE,IAAE,KAAGG,cAAa,MAAI,EAAE,IAAEA,GAAE,OAAKJ,KAAE,GAAGI,GAAE,EAAE,EAAE,CAAC,GAAEA,KAAE,IAAI,GAAGJ,IAAEI,GAAE,GAAEA,GAAE,GAAEA,GAAE,EAAE,IAAGA;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGG,EAAC,GAAEA,KAAEA,GAAE,aAAY,KAAGL,KAAE,GAAGA,IAAEC,IAAEC,EAAC,GAAEF,KAAE,IAAIK,GAAEL,EAAC,GAAE,KAAG,QAAOA;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAED,MAAG,IAAED,KAAE,KAAG,IAAGG,KAAE,CAAC,EAAE,KAAGH;AAAG,SAAOK,KAAE,SAASA,IAAEL,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEG,EAAC;AAAE,QAAIF,KAAED,GAAE;AAAO,UAAME,KAAE,MAAIJ,KAAEE,GAAEC,KAAE,CAAC,IAAE;AAAO,SAAIA,MAAGC,KAAE,KAAG,GAAEJ,KAAE,MAAIA,KAAE,IAAE,GAAEA,KAAEG,IAAEH;AAAI,MAAAE,GAAEF,EAAC,IAAEC,GAAEC,GAAEF,EAAC,CAAC;AAAE,QAAGI,IAAE;AAAC,MAAAJ,KAAEE,GAAEF,EAAC,IAAE,CAAC;AAAE,iBAAUK,MAAKD;AAAE,QAAAJ,GAAEK,EAAC,IAAEJ,GAAEG,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,GAAGH,IAAEG,EAAC,GAAEH;AAAA,EAAC,EAAEG,IAAEL,IAAG,CAAAK,OAAG,GAAGA,IAAEF,IAAED,EAAC,CAAE,GAAE,EAAEG,IAAE,MAAIJ,KAAE,IAAE,EAAE,GAAEI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAML,KAAEK,GAAE,GAAEJ,KAAE,GAAGD,EAAC;AAAE,SAAO,IAAEC,KAAE,GAAGI,IAAEL,IAAEC,IAAE,KAAE,IAAEI;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAM,EAAE,IAAEF,OAAI,QAAMC,OAAI,CAACC,MAAG,MAAID,OAAI,OAAKD,MAAG,OAAKA,QAAKK,GAAE,YAAY,CAAC,IAAE,KAAG,IAAEA,GAAE,YAAY,CAAC,MAAI,KAAG,EAAE,GAAE,MAAIJ,MAAG,EAAEA,KAAED;AAAG;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,GAAGK,KAAEA,GAAE,GAAE,GAAGA,EAAC,GAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAG,GAAGF,KAAEE,MAAG,CAAC,CAAC,EAAE,MAAIF,MAAG,MAAI,KAAGA,MAAGK,GAAE,UAAQL,MAAGC;AAAG,WAAOI,GAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAG,OAAKD;AAAE,WAAO;AAAK,QAAME,KAAEH,MAAG,KAAG,QAAM;AAAU,MAAG,EAAEC,MAAGE,KAAG;AAAC,QAAIC,KAAEC,GAAE;AAAO,WAAOH,MAAG,MAAIF,MAAG,SAAOE,KAAEG,GAAED,KAAE,CAAC,EAAEH,EAAC,MAAI,GAAGI,IAAEL,IAAEG,IAAEF,EAAC,KAAG,QAAM,OAAKD,MAAGK,KAAE,UAAI,CAAC,IAAG,CAAC,KAAG,MAAI,MAAIA,GAAE,CAAC,IAAEL,KAAE,GAAE,EAAE,KAAIE,MAAG,GAAGG,IAAEL,IAAEG,IAAEF,EAAC;AAAA,EAAC;AAAC,SAAO,MAAID,KAAEK,GAAEA,GAAE,SAAO,CAAC,EAAEJ,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAEG,GAAE;AAAE,MAAIF,KAAE,GAAGD,EAAC;AAAE,SAAO,GAAGC,EAAC,GAAE,GAAGD,IAAEC,IAAEH,IAAEC,EAAC,GAAEI;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,MAAG,KAAG,QAAM;AAAU,MAAGC,MAAGE,IAAE;AAAC,QAAIC,IAAEE,KAAEN;AAAE,QAAG,MAAIA;AAAE,MAAAI,KAAEC,GAAEA,GAAE,SAAO,CAAC;AAAA,SAAM;AAAC,UAAG,QAAMH;AAAE,eAAOI;AAAE,MAAAF,KAAEC,GAAEF,MAAG,CAAC,CAAC,EAAE,MAAIH,MAAG,EAAE,IAAE,CAAC,GAAEM,MAAG;AAAA,IAAG;AAAC,WAAOF,GAAEH,EAAC,IAAEC,IAAED,KAAEE,OAAIE,GAAEJ,MAAG,CAAC,CAAC,EAAE,MAAID,MAAG,EAAE,IAAE,SAAQM,OAAIN,MAAG,GAAGK,IAAEC,EAAC,GAAEA;AAAA,EAAC;AAAC,SAAOD,GAAEJ,MAAG,CAAC,CAAC,EAAE,MAAID,MAAG,EAAE,IAAEE,IAAE,MAAIF,OAAIC,OAAKI,KAAEA,GAAEA,GAAE,SAAO,CAAC,MAAI,OAAOA,GAAEJ,EAAC,IAAGD;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAEJ;AAAE,EAAAG,KAAE,GAAGE,IAAEL,IAAEC,IAAEE,EAAC,GAAE,MAAM,QAAQA,EAAC,MAAIA,KAAE;AAAI,QAAMG,KAAE,EAAE,IAAEJ;AAAG,EAAAA,KAAE,EAAE,IAAEA;AAAG,QAAMK,KAAE,CAAC,EAAE,KAAGP;AAAG,MAAIQ,KAAE,GAAGL,EAAC;AAAE,SAAO,MAAIK,MAAG,CAACD,MAAGH,MAAGE,KAAE,IAAEE,OAAIA,MAAG,GAAE,GAAGL,IAAEK,EAAC,MAAIA,MAAG,IAAG,GAAGL,IAAEK,EAAC,IAAGJ,MAAGC,KAAE,OAAG,IAAEG,OAAI,GAAGL,EAAC,GAAEE,KAAE,CAAC,EAAE,IAAEG,OAAKN,MAAGG,OAAI,OAAO,OAAOF,EAAC,MAAIC,KAAE,CAAC,EAAE,IAAEI,OAAI,CAAC,EAAE,OAAKA,KAAGN,MAAGE,MAAGD,KAAE,EAAEA,EAAC,GAAEC,KAAE,GAAEG,MAAG,CAACD,OAAIF,MAAG,KAAI,GAAGD,IAAEC,EAAC,GAAE,GAAGC,IAAEL,IAAEC,IAAEE,EAAC,KAAGG,MAAG,KAAGE,MAAG,CAACJ,MAAG,EAAED,IAAE,EAAE,IAAGA;AAAC;AAAC,SAAS,GAAGE,IAAEL,IAAE;AAAC,EAAAK,KAAEA,GAAE;AAAE,MAAIJ,KAAE,GAAGI,EAAC;AAAE,QAAMH,KAAE,GAAGG,IAAEJ,IAAED,EAAC,GAAEG,KAAE,GAAGD,EAAC;AAAE,SAAO,QAAMC,MAAGA,OAAID,MAAG,GAAGG,IAAEJ,IAAED,IAAEG,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGE,IAAE;AAAC,EAAAA,KAAEA,GAAE;AAAE,MAAIL,KAAE,GAAGK,EAAC;AAAE,QAAMJ,KAAE,GAAGI,IAAEL,IAAE,CAAC,GAAEE,KAAE,GAAGD,IAAE,MAAG,CAAC,EAAE,KAAGD,GAAE;AAAE,SAAO,QAAME,MAAGA,OAAID,MAAG,GAAGI,IAAEL,IAAE,GAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,KAAI;AAAC,SAAO,WAAS,KAAG,IAAE;AAAC;AAAC,SAAS,GAAGG,IAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAME,KAAED,GAAE;AAAE,MAAIE,KAAE,GAAGD,EAAC;AAAE,EAAAJ,KAAE,IAAEK,KAAE,IAAEL,IAAEE,KAAE,CAAC,CAACA,IAAED,KAAE,GAAGG,IAAEC,IAAEP,IAAEG,EAAC;AAAE,MAAIK,KAAE,GAAGL,EAAC,GAAEM,KAAEN;AAAE,MAAG,GAAGM,IAAEJ,EAAC,GAAE,MAAIH,MAAG,MAAIA,MAAG,GAAGO,IAAEJ,EAAC,GAAE,GAAGA,IAAEG,IAAE,QAAOJ,EAAC,GAAE;AAAC,QAAEI,OAAIL,KAAE,EAAEA,EAAC,GAAEK,KAAE,GAAGA,IAAED,EAAC,GAAEA,KAAE,GAAGD,IAAEC,IAAEP,IAAEG,EAAC;AAAG,QAAIE,KAAEI,KAAE;AAAE,WAAKA,KAAEN,GAAE,QAAOM,MAAI;AAAC,YAAMT,KAAEC,GAAEE,GAAEM,EAAC,CAAC;AAAE,cAAMT,OAAIG,GAAEE,IAAG,IAAEL;AAAA,IAAE;AAAC,IAAAK,KAAEI,OAAIN,GAAE,SAAOE,KAAGG,KAAE,SAAO,MAAIA,KAAE,GAAGA,IAAED,EAAC,KAAI,GAAGJ,IAAEK,MAAG,KAAK,GAAE,IAAEA,MAAG,OAAO,OAAOL,EAAC;AAAA,EAAC;AAAC,MAAIQ;AAAE,SAAO,MAAIT,MAAG,MAAIA,MAAG,KAAGM,KAAE,GAAGA,EAAC,MAAIH,KAAEG,KAAGA,MAAG,OAAKH,MAAG,GAAGF,IAAEK,EAAC,GAAE,OAAO,OAAOL,EAAC,MAAIF,KAAE,MAAIC,OAAI,CAAC,EAAE,KAAGM,OAAI,GAAGA,EAAC,KAAG,CAAC,EAAC,yBAAI,IAAIL,QAAK,MAAID,MAAGD,OAAI,GAAGO,EAAC,MAAIL,KAAE,EAAEA,EAAC,GAAEK,KAAE,GAAGA,KAAE,GAAGA,IAAED,EAAC,GAAEA,IAAEH,EAAC,GAAE,GAAGD,IAAEK,EAAC,GAAED,KAAE,GAAGD,IAAEC,IAAEP,IAAEG,EAAC,IAAG,GAAGK,EAAC,MAAIR,KAAEQ,KAAGA,KAAE,GAAGA,IAAED,IAAEH,EAAC,OAAKJ,MAAG,GAAGG,IAAEK,EAAC,IAAGP,MAAGU,KAAE,GAAGR,EAAC,GAAE,GAAGA,IAAEE,IAAE,IAAE,KAAG,MAAIH,MAAGE,OAAG,yBAAI,OAAOD,OAAIQ,MAAGR;AAAC;AAAC,SAAS,GAAGE,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAOG,KAAE,GAAGA,IAAEL,IAAEC,IAAEC,EAAC,GAAE,MAAM,QAAQG,EAAC,IAAEA,KAAE;AAAE;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,SAAO,MAAIK,OAAIA,KAAE,GAAGA,IAAEL,EAAC,IAAG,IAAEK;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,CAAC,EAAE,IAAEA,OAAI,CAAC,EAAE,IAAEA,OAAI,CAAC,EAAE,OAAKA;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,EAAAA,KAAE,EAAEA,EAAC;AAAE,WAAQL,KAAE,GAAEA,KAAEK,GAAE,QAAOL,MAAI;AAAC,UAAMC,KAAEI,GAAEL,EAAC,IAAE,EAAEK,GAAEL,EAAC,CAAC;AAAE,UAAM,QAAQC,GAAE,CAAC,CAAC,MAAIA,GAAE,CAAC,IAAE,GAAGA,GAAE,CAAC,CAAC;AAAA,EAAE;AAAC,SAAOI;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,EAAAG,KAAEA,GAAE;AAAE,MAAIF,KAAE,GAAGE,EAAC;AAAE,KAAGF,EAAC,GAAE,GAAGE,IAAEF,IAAEH,KAAG,QAAME,KAAE,MAAI,OAAOD,EAAC,IAAEA,OAAIC,MAAG,SAAOD,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAE;AAAC,MAAIC,KAAE;AAAG,SAAO,GAAG,GAAGI,KAAEA,GAAE,CAAC,GAAEA,IAAE,GAAGA,EAAC,GAAEJ,EAAC,MAAID,KAAEA,KAAE;AAAE;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAG;AAAE,WAAOA,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,oBAAI;AAAK,MAAG,KAAKA;AAAE,WAAOA,GAAE,CAAC;AAAE,QAAML,KAAE,oBAAI;AAAI,SAAO,OAAO,eAAeK,IAAE,GAAE,EAAC,OAAML,GAAC,CAAC,GAAEA;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGE,EAAC,GAAED,KAAE,GAAGD,IAAEE,IAAEL,IAAEC,EAAC;AAAE,SAAOG,OAAIF,OAAIE,OAAIJ,KAAE,GAAGK,IAAEL,IAAEI,EAAC,IAAGD,GAAE,IAAIF,IAAEC,EAAC,IAAGF;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEE,GAAE,IAAIH,EAAC;AAAE,MAAG,QAAMC;AAAE,WAAOA;AAAE,EAAAA,KAAE;AAAE,WAAQE,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,UAAMD,KAAEF,GAAEG,EAAC;AAAE,YAAM,GAAGL,IAAEC,IAAEG,EAAC,MAAI,MAAID,OAAIF,KAAE,GAAGD,IAAEC,IAAEE,EAAC,IAAGA,KAAEC;AAAA,EAAE;AAAC,SAAOC,GAAE,IAAIH,IAAEC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGE,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,GAAGC,EAAC;AAAE,MAAG,SAAOH,KAAE,GAAGG,IAAED,IAAEH,IAAEC,EAAC,MAAIA,GAAE,MAAI;AAAG,YAAOF,KAAE,GAAGE,EAAC,OAAKA,MAAG,GAAGG,IAAED,IAAEH,IAAED,EAAC,GAAEA,GAAE;AAAE,MAAG,MAAM,QAAQE,EAAC,GAAE;AAAC,UAAMG,KAAE,GAAGH,EAAC;AAAE,IAAAC,KAAE,IAAEE,KAAE,GAAGH,IAAEG,IAAE,KAAE,IAAEH,IAAEC,KAAE,GAAGA,IAAEH,EAAC;AAAA,EAAC;AAAM,IAAAG,KAAE,GAAG,QAAOH,EAAC;AAAE,SAAOG,OAAID,MAAG,GAAGG,IAAED,IAAEH,IAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGE,IAAEL,IAAEC,IAAEC,IAAE;AAAC,EAAAG,KAAEA,GAAE;AAAE,MAAIF,KAAE,GAAGE,EAAC;AAAE,UAAOL,KAAE,GAAGE,KAAE,GAAGG,IAAEF,IAAEF,IAAEC,EAAC,GAAEF,IAAE,OAAGG,EAAC,OAAKD,MAAG,QAAMF,MAAG,GAAGK,IAAEF,IAAEF,IAAED,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,KAAE,OAAG;AAAC,MAAG,SAAOF,KAAE,GAAGK,IAAEL,IAAEC,IAAEC,EAAC;AAAG,WAAOF;AAAE,MAAGK,KAAEA,GAAE,GAAE,EAAE,KAAGH,KAAE,GAAGG,EAAC,KAAI;AAAC,UAAMF,KAAE,GAAGH,EAAC;AAAE,IAAAG,OAAIH,MAAG,GAAGK,IAAEH,IAAED,IAAED,KAAEG,EAAC;AAAA,EAAC;AAAC,SAAOH;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,QAAMC,KAAEF,GAAE;AAAE,MAAIG,KAAE,CAAC,EAAE,IAAER;AAAG,EAAAG,KAAEK,KAAE,IAAEL,IAAEC,KAAE,CAAC,CAACA,IAAEE,YAAI,CAACE,KAAEA,KAAE,GAAGD,IAAEP,IAAEE,EAAC;AAAE,MAAIO,KAAE,GAAGD,EAAC,GAAEG,KAAEH;AAAE,MAAG,GAAGG,IAAEN,EAAC,GAAE,MAAIF,MAAG,MAAIA,MAAG,GAAGQ,IAAEN,EAAC,GAAE,EAAEM,KAAE,CAAC,EAAE,IAAEF,MAAI;AAAC,QAAIG,KAAEJ,IAAEK,KAAEb;AAAE,UAAMK,KAAE,CAAC,EAAE,KAAGI,KAAE,GAAGA,IAAET,EAAC;AAAI,IAAAK,OAAIQ,MAAG;AAAG,QAAIX,KAAE,CAACG,IAAEF,KAAE,MAAGC,KAAE,GAAEE,KAAE;AAAE,WAAKF,KAAEQ,GAAE,QAAOR,MAAI;AAAC,YAAMJ,KAAE,GAAGY,GAAER,EAAC,GAAEH,IAAE,OAAGY,EAAC;AAAE,UAAGb,cAAaC,IAAE;AAAC,YAAG,CAACI,IAAE;AAAC,gBAAMA,KAAE,CAAC,EAAE,IAAE,GAAGL,GAAE,CAAC;AAAG,UAAAE,YAAI,CAACG,KAAEF,YAAIE;AAAA,QAAC;AAAC,QAAAO,GAAEN,IAAG,IAAEN;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAM,KAAEF,OAAIQ,GAAE,SAAON,KAAGG,MAAG,GAAEA,KAAEN,KAAE,KAAGM,KAAE,MAAIA,IAAE,GAAGG,IAAEH,KAAEP,KAAE,IAAEO,KAAE,KAAGA,EAAC,GAAEJ,MAAG,OAAO,OAAOO,EAAC;AAAA,EAAC;AAAC,MAAGN,MAAG,EAAE,IAAEG,MAAG,CAACD,GAAE,WAAS,MAAIL,MAAG,MAAIA,MAAG,KAAGM,MAAI;AAAC,SAAI,GAAGA,EAAC,KAAGD,KAAE,EAAEA,EAAC,GAAEC,KAAE,GAAGA,IAAET,EAAC,GAAEA,KAAE,GAAGO,IAAEP,IAAEE,IAAEM,EAAC,KAAG,GAAGA,IAAEH,EAAC,GAAEJ,KAAEO,IAAEF,KAAEG,IAAEG,KAAE,GAAEA,KAAEX,GAAE,QAAOW;AAAI,OAACH,KAAER,GAAEW,EAAC,QAAMC,KAAE,GAAGJ,EAAC,OAAKR,GAAEW,EAAC,IAAEC;AAAG,IAAAP,MAAG,GAAEA,KAAEL,GAAE,SAAO,MAAIK,KAAE,KAAGA,IAAE,GAAGL,IAAEK,EAAC,GAAEG,KAAEH;AAAA,EAAC;AAAC,MAAIQ;AAAE,SAAO,MAAIX,MAAG,MAAIA,MAAG,KAAGM,KAAE,GAAGA,EAAC,MAAIJ,KAAEI,KAAGA,MAAG,CAACD,GAAE,UAAQ,KAAGC,OAAI,CAACE,MAAG,KAAGF,MAAG,IAAE,UAAQJ,MAAG,GAAGG,IAAEC,EAAC,GAAE,OAAO,OAAOD,EAAC,MAAIG,KAAE,MAAIR,OAAI,CAAC,EAAE,KAAGM,OAAI,GAAGA,EAAC,KAAG,CAAC,EAAC,yBAAI,IAAID,QAAK,MAAIL,MAAGQ,OAAI,GAAGF,EAAC,MAAID,KAAE,EAAEA,EAAC,GAAEC,KAAE,GAAGA,KAAE,GAAGA,IAAET,EAAC,GAAEA,IAAEI,EAAC,GAAE,GAAGI,IAAEC,EAAC,GAAET,KAAE,GAAGO,IAAEP,IAAEE,IAAEM,EAAC,IAAG,GAAGC,EAAC,MAAIP,KAAEO,KAAGA,KAAE,GAAGA,IAAET,IAAEI,EAAC,OAAKF,MAAG,GAAGM,IAAEC,EAAC,IAAGE,MAAGG,KAAE,GAAGN,EAAC,GAAE,GAAGA,IAAEH,IAAE,IAAE,KAAG,MAAIF,MAAGC,OAAG,yBAAI,OAAOI,OAAIM,MAAGN;AAAC;AAAC,SAAS,GAAGH,IAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGG,GAAE,CAAC;AAAE,SAAO,GAAGA,IAAEH,IAAEF,IAAEC,IAAE,GAAG,GAAE,OAAG,EAAE,IAAEC,GAAE;AAAC;AAAC,SAAS,GAAGG,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAO,QAAMA,OAAIA,KAAE,SAAQ,GAAGG,IAAEJ,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGG,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMA,OAAIA,KAAE;AAAQ,KAAE;AAAC,IAAAG,KAAEA,GAAE;AAAE,QAAIF,KAAE,GAAGE,EAAC;AAAE,QAAG,GAAGF,EAAC,GAAE,QAAMD,IAAE;AAAC,YAAMA,KAAE,GAAGG,EAAC;AAAE,UAAG,GAAGH,IAAEG,IAAEF,IAAEF,EAAC,MAAID;AAAE,cAAM;AAAE,MAAAE,GAAE,IAAID,IAAE,CAAC;AAAA,IAAC;AAAM,MAAAE,KAAE,GAAGE,IAAEF,IAAEF,IAAED,EAAC;AAAE,OAAGK,IAAEF,IAAEH,IAAEE,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGG,IAAEL,IAAE;AAAC,SAAM,SAAOK,KAAE,MAAI,IAAEL,KAAE,IAAEK,KAAE,KAAGA;AAAG;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,SAAO,KAAGD,MAAGC,OAAII,MAAG,MAAKA;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGE,GAAE,CAAC;AAAE,KAAGF,EAAC,GAAEE,KAAE,GAAGA,IAAEF,IAAEF,IAAED,IAAE,GAAE,IAAE,GAAEC,KAAE,QAAMC,KAAEA,KAAE,IAAID,MAAEI,GAAE,KAAKJ,EAAC,GAAE,IAAE,GAAGA,GAAE,CAAC,IAAE,EAAEI,IAAE,CAAC,IAAE,EAAEA,IAAE,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,SAAOK,MAAGL;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,GAAG,GAAGK,IAAEL,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,GAAG,GAAGK,IAAEL,EAAC,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,GAAG,GAAG,GAAGK,IAAEL,EAAC,CAAC,GAAE,EAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,aAAW,OAAOA;AAAE,UAAMI,KAAE,OAAOJ,IAAE,MAAM,4BAA4B,YAAUI,KAAEA,KAAEJ,KAAE,MAAM,QAAQA,EAAC,IAAE,UAAQI,KAAE,MAAM,KAAKJ,EAAC,EAAE;AAAE,KAAGI,IAAEL,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,MAAG,QAAMA,IAAE;AAAC,QAAG,YAAU,OAAOA;AAAE,YAAM,EAAE,OAAO;AAAE,QAAG,CAAC,OAAO,SAASA,EAAC;AAAE,YAAM,EAAE,OAAO;AAAE,IAAAA,MAAG;AAAA,EAAC;AAAC,KAAGI,IAAEL,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,YAAU,OAAOA;AAAE,UAAM,MAAM,uDAAuD,OAAOA,EAAC,KAAKA,EAAC,EAAE;AAAE,KAAGI,IAAEL,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC;AAAC,UAAMM,KAAEF,GAAE;AAAE,QAAIG,KAAE,GAAGD,EAAC;AAAE,QAAG,GAAGC,EAAC,GAAE,QAAMP;AAAE,SAAGM,IAAEC,IAAER,EAAC;AAAA,SAAM;AAAC,MAAAC,MAAE,yBAAI,IAAIA,QAAIA;AAAE,UAAIC,IAAEC,KAAE,GAAGF,EAAC,GAAEG,KAAED,IAAEG,KAAE,CAAC,EAAE,IAAEH,OAAI,OAAO,SAASF,EAAC;AAAE,WAAIC,KAAE,CAACI,QAAKJ,KAAE,WAAS,MAAI,QAAI,GAAGG,IAAEF,EAAC,GAAE;AAAC,QAAAA,KAAE,IAAGG,OAAIL,KAAE,EAAEA,EAAC,GAAEG,KAAE,GAAED,KAAE,GAAGA,KAAE,GAAGA,IAAEK,EAAC,GAAEA,IAAE,IAAE;AAAG,iBAAQH,KAAE,GAAEA,KAAEJ,GAAE,QAAOI;AAAI,UAAAJ,GAAEI,EAAC,IAAE,GAAGJ,GAAEI,EAAC,CAAC;AAAA,MAAC;AAAC,MAAAH,MAAGD,KAAE,EAAEA,EAAC,GAAEG,KAAE,GAAED,KAAE,GAAGA,KAAE,GAAGA,IAAEK,EAAC,GAAEA,IAAE,IAAE,KAAGF,MAAG,GAAGL,IAAEI,EAAC,GAAEF,OAAIC,MAAG,GAAGH,IAAEE,EAAC,GAAE,GAAGI,IAAEC,IAAER,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,KAAG,GAAGI,GAAE,CAAC,CAAC,GAAE,GAAGA,IAAEL,IAAE,IAAG,GAAE,QAAO,IAAE,EAAE,KAAK,GAAGC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAE;AAAC,SAAO,MAAM,sBAAsBK,EAAC,iBAAiBL,EAAC,GAAG;AAAC;AAAC,SAAS,KAAI;AAAC,SAAO,MAAM,6CAA6C;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,MAAM,0CAA0CA,EAAC,MAAMK,EAAC,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,YAAU,OAAOA;AAAE,WAAM,EAAC,QAAO,EAAEA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAG,MAAM,QAAQA,EAAC;AAAE,WAAM,EAAC,QAAO,IAAI,WAAWA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc;AAAW,WAAM,EAAC,QAAOA,IAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc;AAAY,WAAM,EAAC,QAAO,IAAI,WAAWA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc;AAAE,WAAM,EAAC,QAAO,EAAEA,EAAC,KAAG,IAAI,WAAW,CAAC,GAAE,GAAE,KAAE;AAAE,MAAGA,cAAa;AAAW,WAAM,EAAC,QAAO,IAAI,WAAWA,GAAE,QAAOA,GAAE,YAAWA,GAAE,UAAU,GAAE,GAAE,MAAE;AAAE,QAAM,MAAM,2IAA2I;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAIC,IAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,QAAME,KAAED,GAAE;AAAE,MAAIE,KAAEF,GAAE;AAAE,KAAE;AAAC,IAAAJ,KAAEK,GAAEC,IAAG,GAAEL,OAAI,MAAID,OAAIG,IAAEA,MAAG;AAAA,EAAC,SAAOA,KAAE,MAAI,MAAIH;AAAG,OAAIG,KAAE,OAAKD,OAAI,MAAIF,OAAI,IAAGG,KAAE,GAAEA,KAAE,MAAI,MAAIH,IAAEG,MAAG;AAAE,IAAAH,KAAEK,GAAEC,IAAG,GAAEJ,OAAI,MAAIF,OAAIG;AAAE,MAAG,GAAGC,IAAEE,EAAC,GAAEN,KAAE;AAAI,WAAOD,GAAEE,OAAI,GAAEC,OAAI,CAAC;AAAE,QAAM,GAAG;AAAC;AAAC,SAAS,GAAGE,IAAE;AAAC,MAAIL,KAAE,GAAEC,KAAEI,GAAE;AAAE,QAAMH,KAAED,KAAE,IAAGE,KAAEE,GAAE;AAAE,SAAKJ,KAAEC,MAAG;AAAC,UAAMA,KAAEC,GAAEF,IAAG;AAAE,QAAGD,MAAGE,IAAE,MAAI,MAAIA;AAAG,aAAO,GAAGG,IAAEJ,EAAC,GAAE,CAAC,EAAE,MAAID;AAAA,EAAE;AAAC,QAAM,GAAG;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,QAAML,KAAEK,GAAE;AAAE,MAAIJ,KAAEI,GAAE,GAAEH,KAAEF,GAAEC,IAAG,GAAEE,KAAE,MAAID;AAAE,MAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,GAAE,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,IAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,IAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,MAAGD,MAAG,IAAG,MAAIA,MAAG,MAAIF,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG;AAAM,UAAM,GAAG;AAAE,SAAO,GAAGI,IAAEJ,EAAC,GAAEE;AAAC;AAAC,SAAS,GAAGE,IAAE;AAAC,SAAO,GAAGA,EAAC,MAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIL,KAAEK,GAAE;AAAE,QAAMJ,KAAEI,GAAE,GAAEH,KAAEF,GAAEC,EAAC,GAAEE,KAAEH,GAAEC,KAAE,CAAC,GAAEG,KAAEJ,GAAEC,KAAE,CAAC;AAAE,SAAOD,KAAEA,GAAEC,KAAE,CAAC,GAAE,GAAGI,IAAEA,GAAE,IAAE,CAAC,IAAGH,MAAG,IAAEC,MAAG,IAAEC,MAAG,KAAGJ,MAAG,QAAM;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAE,GAAGK,EAAC;AAAE,EAAAA,KAAE,KAAGL,MAAG,MAAI;AAAE,QAAMC,KAAED,OAAI,KAAG;AAAI,SAAOA,MAAG,SAAQ,OAAKC,KAAED,KAAE,MAAIK,MAAG,IAAE,KAAG,KAAGJ,KAAE,uBAAqBI,KAAEL,KAAEK,KAAE,KAAK,IAAI,GAAEJ,KAAE,GAAG,KAAGD,KAAE;AAAQ;AAAC,SAAS,GAAGK,IAAE;AAAC,SAAO,GAAGA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE,EAAC,IAAGC,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,EAAAI,GAAE,KAAGJ,IAAED,OAAIA,KAAE,GAAGA,EAAC,GAAEK,GAAE,IAAEL,GAAE,QAAOK,GAAE,IAAEL,GAAE,GAAEK,GAAE,IAAE,GAAEA,GAAE,IAAEA,GAAE,EAAE,QAAOA,GAAE,IAAEA,GAAE;AAAE;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAGK,GAAE,IAAEL,IAAEA,KAAEK,GAAE;AAAE,UAAM,GAAGA,GAAE,GAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,MAAGA,KAAE;AAAE,UAAM,MAAM,yCAAyCA,EAAC,EAAE;AAAE,QAAMC,KAAEI,GAAE,GAAEH,KAAED,KAAED;AAAE,MAAGE,KAAEG,GAAE;AAAE,UAAM,GAAGL,IAAEK,GAAE,IAAEJ,EAAC;AAAE,SAAOI,GAAE,IAAEH,IAAED;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAE;AAAC,MAAG,KAAGA;AAAE,WAAO,EAAE;AAAE,MAAIC,KAAE,GAAGI,IAAEL,EAAC;AAAE,SAAOK,GAAE,MAAIA,GAAE,IAAEJ,KAAEI,GAAE,EAAE,SAASJ,IAAEA,KAAED,EAAC,KAAGK,KAAEA,GAAE,GAAEJ,KAAEA,QAAKD,KAAEC,KAAED,MAAG,IAAI,WAAW,CAAC,IAAE,KAAGK,GAAE,MAAMJ,IAAED,EAAC,IAAE,IAAI,WAAWK,GAAE,SAASJ,IAAED,EAAC,CAAC,IAAG,KAAGC,GAAE,SAAO,EAAE,IAAE,IAAI,EAAEA,IAAE,CAAC;AAAC;AAAC,GAAG,UAAU,SAAO,QAAO,GAAG,UAAU,KAAG;AAAG,IAAI,KAAG,CAAC;AAAE,SAAS,GAAGI,IAAE;AAAC,MAAIL,KAAEK,GAAE;AAAE,MAAGL,GAAE,KAAGA,GAAE;AAAE,WAAM;AAAG,EAAAK,GAAE,IAAEA,GAAE,EAAE;AAAE,MAAIJ,KAAE,GAAGI,GAAE,CAAC;AAAE,MAAGL,KAAEC,OAAI,GAAE,GAAGA,MAAG,MAAI,KAAGA,MAAG;AAAG,UAAM,GAAGA,IAAEI,GAAE,CAAC;AAAE,MAAGL,KAAE;AAAE,UAAM,MAAM,yBAAyBA,EAAC,iBAAiBK,GAAE,CAAC,GAAG;AAAE,SAAOA,GAAE,IAAEL,IAAEK,GAAE,IAAEJ,IAAE;AAAE;AAAC,SAAS,GAAGI,IAAE;AAAC,UAAOA,GAAE,GAAE;AAAA,IAAC,KAAK;AAAE,WAAGA,GAAE,IAAE,GAAGA,EAAC,IAAE,GAAGA,GAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,SAAGA,KAAEA,GAAE,GAAEA,GAAE,IAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,UAAG,KAAGA,GAAE;AAAE,WAAGA,EAAC;AAAA,WAAM;AAAC,YAAIL,KAAE,GAAGK,GAAE,CAAC;AAAE,WAAGA,KAAEA,GAAE,GAAEA,GAAE,IAAEL,EAAC;AAAA,MAAC;AAAC;AAAA,IAAM,KAAK;AAAE,SAAGK,KAAEA,GAAE,GAAEA,GAAE,IAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,WAAIL,KAAEK,GAAE,OAAI;AAAC,YAAG,CAAC,GAAGA,EAAC;AAAE,gBAAM,MAAM,uCAAuC;AAAE,YAAG,KAAGA,GAAE,GAAE;AAAC,cAAGA,GAAE,KAAGL;AAAE,kBAAM,MAAM,yBAAyB;AAAE;AAAA,QAAK;AAAC,WAAGK,EAAC;AAAA,MAAC;AAAC;AAAA,IAAM;AAAQ,YAAM,GAAGA,GAAE,GAAEA,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAEG,GAAE,EAAE,GAAEF,KAAE,GAAGE,GAAE,CAAC,GAAED,KAAEC,GAAE,EAAE,IAAEF;AAAE,MAAIG,KAAEF,KAAEF;AAAE,MAAGI,MAAG,MAAID,GAAE,EAAE,IAAED,IAAEH,GAAED,IAAEK,IAAE,QAAO,QAAO,MAAM,GAAEC,KAAEF,KAAEC,GAAE,EAAE,IAAGC;AAAE,UAAM,MAAM,wDAAwDH,EAAC,wBAAwBA,KAAEG,EAAC,sFAAsF;AAAE,SAAOD,GAAE,EAAE,IAAED,IAAEC,GAAE,EAAE,IAAEH,IAAEF;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAE,GAAGK,GAAE,CAAC,GAAEE,KAAE,GAAGF,KAAEA,GAAE,GAAEL,EAAC;AAAE,MAAGK,KAAEA,GAAE,GAAE,GAAE;AAAC,QAAIG,IAAEC,KAAEJ;AAAE,KAACG,KAAE,OAAKA,KAAE,IAAE,IAAI,YAAY,SAAQ,EAAC,OAAM,KAAE,CAAC,IAAGR,KAAEO,KAAEP,IAAES,KAAE,MAAIF,MAAGP,OAAIS,GAAE,SAAOA,KAAEA,GAAE,SAASF,IAAEP,EAAC;AAAE,QAAG;AAAC,UAAIW,KAAEH,GAAE,OAAOC,EAAC;AAAA,IAAC,SAAOJ,IAAE;AAAC,UAAG,WAAS,GAAE;AAAC,YAAG;AAAC,UAAAG,GAAE,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AAAA,QAAC,SAAOH,IAAE;AAAA,QAAC;AAAC,YAAG;AAAC,UAAAG,GAAE,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC,GAAE,IAAE;AAAA,QAAE,SAAOH,IAAE;AAAC,cAAE;AAAA,QAAE;AAAA,MAAC;AAAC,YAAK,CAAC,MAAI,IAAE,SAAQA;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,IAAAL,MAAGW,KAAEJ,MAAGP,IAAEO,KAAE,CAAC;AAAE,QAAIJ,IAAEC,KAAE;AAAK,WAAKO,KAAEX,MAAG;AAAC,UAAIY,KAAEP,GAAEM,IAAG;AAAE,MAAAC,KAAE,MAAIL,GAAE,KAAKK,EAAC,IAAEA,KAAE,MAAID,MAAGX,KAAE,EAAE,KAAGG,KAAEE,GAAEM,IAAG,GAAEC,KAAE,OAAK,QAAM,MAAIT,OAAIQ,MAAI,EAAE,KAAGJ,GAAE,MAAM,KAAGK,OAAI,IAAE,KAAGT,EAAC,KAAGS,KAAE,MAAID,MAAGX,KAAE,IAAE,EAAE,KAAGG,KAAEE,GAAEM,IAAG,GAAE,QAAM,MAAIR,OAAI,QAAMS,MAAGT,KAAE,OAAK,QAAMS,MAAGT,MAAG,OAAK,QAAM,OAAKK,KAAEH,GAAEM,IAAG,OAAKA,MAAI,EAAE,KAAGJ,GAAE,MAAM,KAAGK,OAAI,MAAI,KAAGT,OAAI,IAAE,KAAGK,EAAC,KAAGI,MAAG,MAAID,MAAGX,KAAE,IAAE,EAAE,KAAGG,KAAEE,GAAEM,IAAG,GAAE,QAAM,MAAIR,OAAIA,KAAE,OAAKS,MAAG,OAAK,MAAI,KAAG,QAAM,OAAKJ,KAAEH,GAAEM,IAAG,OAAK,QAAM,OAAKF,KAAEJ,GAAEM,IAAG,OAAKA,MAAI,EAAE,MAAIC,MAAG,IAAEA,OAAI,MAAI,KAAGT,OAAI,MAAI,KAAGK,OAAI,IAAE,KAAGC,IAAEG,MAAG,OAAML,GAAE,KAAK,SAAOK,MAAG,KAAG,OAAM,SAAO,OAAKA,GAAE,MAAI,EAAE,GAAEL,GAAE,UAAQ,SAAOH,KAAE,EAAEA,IAAEG,EAAC,GAAEA,GAAE,SAAO;AAAA,IAAE;AAAC,IAAAI,KAAE,EAAEP,IAAEG,EAAC;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAAS,GAAGN,IAAE;AAAC,QAAML,KAAE,GAAGK,GAAE,CAAC;AAAE,SAAO,GAAGA,GAAE,GAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,MAAIC,KAAE,GAAGG,GAAE,CAAC;AAAE,OAAIH,KAAEG,GAAE,EAAE,IAAEH,IAAEG,GAAE,EAAE,IAAEH;AAAG,IAAAD,GAAE,KAAKD,GAAEK,GAAE,CAAC,CAAC;AAAC;AAAC,IAAI,KAAG,CAAC;AAAE,IAAI;AAAG,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAEA,GAAE,EAAEK,IAAEL,GAAE,GAAEA,GAAE,GAAEC,IAAE,IAAE,IAAED,GAAE,EAAEK,IAAEL,GAAE,GAAEC,IAAE,IAAE;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYI,IAAEL,IAAE;AAAC,SAAK,IAAE,GAAGK,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE;AAAG,WAAOA,GAAE,IAAEA,GAAE,EAAE,MAAKA,GAAE,GAAEA,GAAE,GAAE,IAAE,IAAEA,GAAE,EAAE,MAAKA,GAAE,GAAEA,GAAE,cAAa,IAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,KAAK;AAAE,WAAO,GAAG,MAAKA,IAAE,GAAGA,EAAC,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,EAAE,IAAE,GAAG,KAAK,CAAC;AAAA,EAAE;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,KAAGA,EAAC,GAAEA,KAAE,KAAGA,GAAE,IAAE,GAAGA,GAAE,GAAE,IAAG,QAAO,QAAO,KAAE;AAAE;AAAC,QAAIL,KAAE,CAAC;AAAG,QAAIS,KAAEJ,GAAE;AAAO,QAAGI,IAAE;AAAC,UAAIR,KAAEI,GAAEI,KAAE,CAAC,GAAEP,KAAE,GAAGD,EAAC;AAAE,MAAAC,KAAEO,OAAIR,KAAE;AAAO,UAAIE,KAAEE;AAAE,UAAGH,IAAE;AAAC,WAAE;AAAC,cAAIE,IAAEE,KAAEL,IAAEM,KAAE;AAAG,cAAGD;AAAE,qBAAQD,MAAKC;AAAE,oBAAM,CAACD,EAAC,KAAGD,YAAI,CAAC,IAAGC,EAAC,IAAEC,GAAED,EAAC,KAAGH,KAAEI,GAAED,EAAC,GAAE,MAAM,QAAQH,EAAC,MAAI,GAAGA,EAAC,KAAG,GAAGA,EAAC,KAAG,MAAIA,GAAE,UAAQA,KAAE,OAAM,QAAMA,OAAIK,KAAE,OAAI,QAAML,QAAKE,YAAI,CAAC,IAAGC,EAAC,IAAEH;AAAI,cAAGK,OAAIH,KAAEE,KAAGF;AAAE,qBAAQC,MAAKD,IAAE;AAAC,cAAAG,KAAEH;AAAE,oBAAM;AAAA,YAAC;AAAC,UAAAG,KAAE;AAAA,QAAI;AAAC,QAAAD,KAAE,QAAMC,KAAE,QAAMN,KAAEM,OAAIN;AAAA,MAAC;AAAC,aAAKQ,KAAE,MAAI,SAAOL,KAAED,GAAEM,KAAE,CAAC,MAAI,GAAGL,EAAC,KAAG,GAAGA,EAAC,KAAG,MAAIA,GAAE,OAAMK;AAAI,YAAID,KAAE;AAAG,OAACL,OAAIE,MAAGC,MAAGE,QAAKR,MAAGQ,MAAGF,MAAGC,QAAKJ,GAAE,SAAOM,MAAGN,KAAE,MAAM,UAAU,MAAM,KAAKA,IAAE,GAAEM,EAAC,GAAEF,MAAGJ,GAAE,KAAKI,EAAC,IAAGC,KAAEL;AAAA,IAAC;AAAM,MAAAK,KAAEH;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,SAAOA,KAAE,QAAQ,KAAKA,EAAC,KAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,IAAG,EAAE,KAAG,OAAK,YAAK,IAAI,GAAG,GAAE,CAAC;AAAC;AAAC,GAAG,UAAU,IAAE,IAAG,GAAG,UAAU,WAAS,WAAU;AAAC,MAAG;AAAC,WAAO,KAAG,MAAG,GAAG,IAAI,EAAE,SAAS;AAAA,EAAC,UAAC;AAAQ,SAAG;AAAA,EAAE;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,SAAK,IAAEK,OAAI,GAAE,KAAK,IAAEL,OAAI;AAAA,EAAC;AAAC;AAAE,IAAI;AAAG,SAAS,GAAGK,IAAE;AAAC,SAAOA,KAAE,UAAU,KAAKA,EAAC,KAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,IAAG,EAAE,KAAG,OAAK,YAAK,IAAI,GAAG,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,SAAK,IAAEK,OAAI,GAAE,KAAK,IAAEL,OAAI;AAAA,EAAC;AAAC;AAAE,IAAI;AAAG,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,SAAKA,KAAE,KAAGD,KAAE;AAAK,IAAAK,GAAE,EAAE,KAAK,MAAIL,KAAE,GAAG,GAAEA,MAAGA,OAAI,IAAEC,MAAG,QAAM,GAAEA,QAAK;AAAE,EAAAI,GAAE,EAAE,KAAKL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAKA,KAAE;AAAK,IAAAK,GAAE,EAAE,KAAK,MAAIL,KAAE,GAAG,GAAEA,QAAK;AAAE,EAAAK,GAAE,EAAE,KAAKL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,MAAGA,MAAG;AAAE,OAAGK,IAAEL,EAAC;AAAA,OAAM;AAAC,aAAQC,KAAE,GAAEA,KAAE,GAAEA;AAAI,MAAAI,GAAE,EAAE,KAAK,MAAIL,KAAE,GAAG,GAAEA,OAAI;AAAE,IAAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,EAAAK,GAAE,EAAE,KAAKL,OAAI,IAAE,GAAG,GAAEK,GAAE,EAAE,KAAKL,OAAI,IAAE,GAAG,GAAEK,GAAE,EAAE,KAAKL,OAAI,KAAG,GAAG,GAAEK,GAAE,EAAE,KAAKL,OAAI,KAAG,GAAG;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,QAAIA,GAAE,WAASK,GAAE,EAAE,KAAKL,EAAC,GAAEK,GAAE,KAAGL,GAAE;AAAO;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,KAAGI,GAAE,GAAE,IAAEL,KAAEC,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAE;AAAC,SAAO,GAAGK,IAAEL,IAAE,CAAC,GAAEA,KAAEK,GAAE,EAAE,IAAI,GAAE,GAAGA,IAAEL,EAAC,GAAEA,GAAE,KAAKK,GAAE,CAAC,GAAEL;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,MAAIC,KAAED,GAAE,IAAI;AAAE,OAAIC,KAAEI,GAAE,IAAEA,GAAE,EAAE,OAAO,IAAEJ,IAAEA,KAAE;AAAK,IAAAD,GAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK,GAAEI,GAAE;AAAI,EAAAL,GAAE,KAAKC,EAAC,GAAEI,GAAE;AAAG;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,KAAGI,IAAEL,IAAE,CAAC,GAAE,GAAGK,GAAE,GAAEJ,GAAE,MAAM,GAAE,GAAGI,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAE,GAAGA,IAAEJ,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMD,OAAID,KAAE,GAAGK,IAAEL,EAAC,GAAEE,GAAED,IAAEI,EAAC,GAAE,GAAGA,IAAEL,EAAC;AAAE;AAAC,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYK,IAAEL,IAAEC,IAAE;AAAC,SAAK,IAAEI,IAAE,KAAK,IAAEL,IAAE,KAAK,KAAGC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,SAAO,MAAM,QAAQA,EAAC,IAAEA,GAAE,CAAC,aAAY,KAAGA,KAAE,CAAC,IAAGA,EAAC,IAAE,CAACA,IAAE,MAAM;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,QAAIC,KAAE,GAAGD,EAAC;AAAE,QAAG,IAAEC;AAAE,aAAOD;AAAE,aAAQE,KAAE,GAAEC,KAAE,GAAED,KAAEF,GAAE,QAAOE,MAAI;AAAC,YAAMD,KAAEI,GAAEL,GAAEE,EAAC,CAAC;AAAE,cAAMD,OAAID,GAAEG,IAAG,IAAEF;AAAA,IAAE;AAAC,WAAOE,KAAED,OAAIF,GAAE,SAAOG,KAAG,GAAGH,IAAE,UAAQ,IAAEC,GAAE,GAAE,IAAEA,MAAG,OAAO,OAAOD,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG,OAAO;AAAE,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAEK,GAAE,EAAE;AAAE,MAAG,CAACL,IAAE;AAAC,UAAMC,KAAE,GAAGI,EAAC,GAAEH,KAAED,GAAE;AAAE,IAAAD,KAAEE,KAAE,CAACG,IAAEL,OAAIE,GAAEG,IAAEL,IAAEC,EAAC,IAAE,CAACI,IAAEL,OAAI;AAAtk4B,UAAAU;AAAuk4B,aAAK,GAAGV,EAAC,KAAG,KAAGA,GAAE,KAAG;AAAC,YAAIE,KAAEF,GAAE;AAAE,YAAIM,KAAEL,GAAEC,EAAC;AAAE,cAAMK,KAAE,CAACD;AAAE,YAAIE,KAAE;AAAG,YAAG,CAACF,IAAE;AAAC,cAAIH,KAAEF,GAAE;AAAE,cAAGE,IAAE;AAAC,gBAAIC,KAAED,GAAED,EAAC;AAAE,YAAAE,OAAII,MAAEE,MAAAP,GAAE,MAAF,gBAAAO,IAAMR,MAAI,CAAC,KAAGM,QAAKL,KAAE,GAAGC,EAAC,OAAKE,KAAEL,GAAEC,EAAC,IAAEC;AAAA,UAAG;AAAA,QAAC;AAAC,QAAAG,MAAGA,GAAEN,IAAEK,IAAEH,EAAC,MAAIA,MAAGC,KAAEH,IAAG,GAAE,GAAGG,EAAC,GAAEA,GAAE,KAAGA,KAAE,UAAQC,KAAED,GAAE,EAAE,IAAED,IAAEC,GAAE,EAAE,IAAED,IAAEC,KAAE,GAAGA,GAAE,GAAEC,EAAC,IAAGF,KAAEG,IAAEF,OAAI,YAAK,OAAO,KAAGC,KAAEF,GAAE,EAAE,KAAGE,GAAE,KAAKD,EAAC,IAAED,GAAE,EAAE,IAAE,CAACC,EAAC,KAAII,MAAGD,MAAG,CAACE,MAAG,OAAK,KAAG,EAAE;AAAA,MAAC;AAAA,IAAC,GAAEH,GAAE,EAAE,IAAEL;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,QAAML,MAAGK,KAAE,GAAGA,EAAC,GAAG,CAAC,EAAE;AAAE,MAAGA,KAAEA,GAAE,CAAC,GAAE;AAAC,UAAMJ,KAAE,GAAGI,EAAC,GAAEH,KAAE,GAAGG,EAAC,EAAE;AAAE,WAAM,CAACA,IAAEF,IAAEC,OAAIJ,GAAEK,IAAEF,IAAEC,IAAEF,IAAED,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,EAAAI,GAAEL,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAG,EAAAH,GAAE,IAAE,SAASK,IAAE;AAAC,YAAO,OAAOA,IAAE;AAAA,MAAC,KAAI;AAAU,eAAO,YAAK,CAAC,GAAE,QAAO,IAAE;AAAA,MAAE,KAAI;AAAS,eAAOA,KAAE,IAAE,SAAO,MAAIA,KAAE,YAAK,CAAC,GAAE,MAAM,KAAE,CAAC,CAACA,IAAE,MAAM;AAAA,MAAE,KAAI;AAAS,eAAM,CAAC,GAAEA,EAAC;AAAA,MAAE,KAAI;AAAS,eAAOA;AAAA,IAAC;AAAA,EAAC,EAAEA,GAAE,CAAC,CAAC;AAAE,MAAID,KAAE;AAAE,MAAIE,KAAED,GAAE,EAAED,EAAC;AAAE,EAAAE,MAAGA,GAAE,gBAAc,WAASN,GAAE,IAAEM,IAAE,cAAY,QAAOA,KAAED,GAAE,EAAED,EAAC,OAAKJ,GAAE,IAAEM,IAAEN,GAAE,IAAEK,GAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,EAAED,EAAC;AAAI,QAAMG,KAAE,CAAC;AAAE,SAAK,MAAM,QAAQD,EAAC,KAAG,YAAU,OAAOA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,KAAG;AAAC,aAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,MAAAD,GAAED,GAAEE,EAAC,CAAC,IAAEF;AAAE,IAAAA,KAAED,GAAE,EAAED,EAAC;AAAA,EAAC;AAAC,OAAII,KAAE,GAAE,WAASF,MAAG;AAAC,QAAIM;AAAE,gBAAU,OAAON,OAAIE,MAAGF,IAAEA,KAAED,GAAE,EAAED,EAAC;AAAG,QAAIK,KAAE;AAAO,QAAGH,cAAa,KAAGM,KAAEN,MAAGM,KAAE,IAAGR,OAAKQ,GAAE,IAAG;AAAC,MAAAN,KAAED,GAAE,EAAED,EAAC,GAAEK,KAAEJ;AAAE,UAAIM,KAAEP;AAAE,oBAAY,OAAOE,OAAIA,KAAEA,GAAE,GAAEG,GAAEE,EAAC,IAAEL,KAAGG,KAAEH;AAAA,IAAC;AAAC,SAAIK,KAAEH,KAAE,GAAE,YAAU,QAAOF,KAAED,GAAE,EAAED,EAAC,MAAIE,KAAE,MAAIK,MAAGL,IAAEA,KAAED,GAAE,EAAED,EAAC,IAAGI,KAAEG,IAAEH,MAAI;AAAC,YAAMH,KAAEE,GAAEC,EAAC;AAAE,MAAAL,GAAEH,IAAEQ,IAAEC,KAAEP,GAAEU,IAAEH,IAAEJ,EAAC,IAAEJ,GAAEW,IAAEP,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOL;AAAC;AAAC,IAAM,KAAG,OAAO;AAAE,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAEK,GAAE,EAAE;AAAE,MAAG,CAACL,IAAE;AAAC,UAAMC,KAAE,GAAGI,EAAC;AAAE,IAAAL,KAAE,CAACK,IAAEL,OAAI,GAAGK,IAAEL,IAAEC,EAAC,GAAEI,GAAE,EAAE,IAAEL;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,IAAM,KAAG,OAAO;AAAE,SAAS,GAAGK,IAAE;AAAC,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAIC,IAAEC;AAAE,QAAMC,KAAEE,GAAE;AAAE,SAAM,CAACA,IAAED,IAAEE,OAAIH,GAAEE,IAAED,IAAEE,IAAEJ,YAAI,GAAGF,EAAC,EAAE,IAAEC,YAAI,GAAGD,EAAC,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAEK,GAAE,EAAE;AAAE,SAAOL,OAAIA,KAAE,GAAGK,IAAEA,GAAE,EAAE,IAAE,CAAC,GAAE,IAAG,EAAE;AAAE;AAAC,IAAM,KAAG,OAAO;AAAE,SAAS,GAAGA,IAAEL,IAAE;AAAC,QAAMC,KAAEI,GAAE;AAAE,SAAOL,KAAE,CAACK,IAAEH,IAAEC,OAAIF,GAAEI,IAAEH,IAAEC,IAAEH,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAEG,GAAE;AAAE,MAAIF,IAAEC;AAAE,SAAM,CAACC,IAAEC,IAAEC,OAAIL,GAAEG,IAAEC,IAAEC,IAAEH,YAAI,GAAGJ,EAAC,EAAE,IAAEG,YAAI,GAAGH,EAAC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,MAAIL,KAAEK,GAAE,EAAE;AAAE,SAAOL,OAAIA,KAAE,GAAGK,IAAEA,GAAE,EAAE,IAAE,CAAC,GAAE,IAAG,EAAE;AAAE;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAvw7B,MAAAU;AAAww7B,MAAIT,KAAEI,GAAEL,EAAC;AAAE,MAAGC;AAAE,WAAOA;AAAE,MAAGA,KAAEI,GAAE,GAAE;AAAC,QAAIH,KAAED,GAAED,EAAC;AAAE,QAAGE,IAAE;AAAC,UAAIC,MAAGD,KAAE,GAAGA,EAAC,GAAG,CAAC,EAAE;AAAE,UAAGA,KAAEA,GAAE,CAAC,GAAED,MAAES,MAAAT,GAAE,MAAF,gBAAAS,IAAMV,KAAG,CAAC,KAAGC,IAAE;AAAC,YAAGC,IAAE;AAAC,gBAAMF,KAAE,GAAGE,EAAC,GAAEE,KAAE,GAAGF,EAAC,EAAE;AAAE,UAAAD,MAAGA,KAAEI,GAAE,KAAGJ,GAAEG,IAAEJ,EAAC,IAAE,CAACK,IAAEJ,IAAEC,OAAIC,GAAEE,IAAEJ,IAAEC,IAAEE,IAAEJ,EAAC;AAAA,QAAC;AAAM,UAAAC,KAAEE;AAAE,eAAOE,GAAEL,EAAC,IAAEC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAA997B,MAAAS,KAAA;AAA+97B,WAAQR,KAAE,GAAGG,EAAC,GAAEF,KAAE,CAAC,CAAC,EAAE,MAAID,MAAG,GAAEE,KAAEC,GAAE,QAAOC,KAAE,MAAIJ,KAAE,IAAE,GAAEK,KAAEH,MAAG,MAAIF,KAAE,KAAG,IAAGI,KAAEC,IAAED,MAAI;AAAC,UAAMJ,KAAEG,GAAEC,EAAC;AAAE,QAAG,QAAMJ;AAAE;AAAS,UAAME,KAAEE,KAAEH,IAAEI,KAAE,GAAGN,IAAEG,EAAC;AAAE,QAAG,CAACG;AAAE;AAAS,UAAMC,KAAEP,GAAE;AAAE,KAAAO,MAAA,gBAAAA,GAAIJ,QAAI,GAACM,MAAAF,MAAA,gBAAAA,GAAG,MAAH,gBAAAE,IAAON,QAAI,OAAK,KAAG,EAAE,GAAEG,GAAEP,IAAEE,IAAEE,EAAC;AAAA,EAAC;AAAC,MAAG,MAAIF,IAAE;AAAC,IAAAA,KAAEG,GAAED,KAAE,CAAC;AAAE,aAAQC,MAAKH;AAAE,MAAAC,KAAE,CAACE,IAAE,CAAC,OAAO,MAAMF,EAAC,MAAI,SAAOC,KAAEF,GAAEG,EAAC,OAAKE,KAAE,GAAGN,IAAEE,EAAC,UAAO,KAAAG,KAAEL,GAAE,MAAJ,mBAASE,QAAI,GAAC,KAAAG,MAAA,gBAAAA,GAAG,MAAH,mBAAOH,QAAI,OAAK,KAAG,EAAE,GAAEI,GAAEP,IAAEI,IAAED,EAAC;AAAA,EAAE;AAAC,MAAGE,KAAE,KAAGA,GAAE,EAAE,IAAE;AAAO,SAAI,GAAGL,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAEC,KAAE,GAAEA,KAAEI,GAAE,QAAOJ;AAAI,SAAGD,IAAE,EAAEK,GAAEJ,EAAC,CAAC,KAAG,IAAI,WAAW,CAAC,CAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAE;AAAC,SAAO,IAAI,GAAGK,IAAEL,IAAE,KAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,IAAI,GAAGK,IAAEL,IAAE,KAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,IAAI,GAAGK,IAAEL,IAAE,IAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,KAAGI,IAAE,GAAGA,EAAC,GAAEL,IAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,GAAI,SAASI,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIE,GAAE,MAAIA,KAAE,GAAGA,IAAE,GAAG,CAAC,QAAO,MAAM,GAAEH,EAAC,GAAEC,EAAC,GAAE,GAAGD,KAAE,GAAGF,EAAC,CAAC,IAAGG,KAAE,GAAGH,IAAEE,IAAED,EAAC,cAAa,KAAG,MAAI,IAAEE,GAAE,OAAKA,KAAEA,GAAE,EAAE,GAAG,KAAKE,EAAC,GAAE,GAAGL,IAAEE,IAAED,IAAEE,EAAC,KAAGA,GAAE,GAAGE,EAAC,IAAE,MAAM,QAAQF,EAAC,KAAG,IAAE,GAAGA,EAAC,KAAG,GAAGH,IAAEE,IAAED,IAAEE,KAAE,GAAGA,EAAC,CAAC,GAAEA,GAAE,KAAKE,EAAC,KAAG,GAAGL,IAAEE,IAAED,IAAE,CAACI,EAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGH,cAAa;AAAG,IAAAA,GAAE,QAAS,CAACA,IAAEI,OAAI;AAAC,SAAGC,IAAEJ,IAAE,GAAG,CAACG,IAAEJ,EAAC,GAAEE,EAAC,GAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,WAAU,MAAM,QAAQH,EAAC;AAAE,aAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAI;AAAC,YAAME,KAAEN,GAAEI,EAAC;AAAE,YAAM,QAAQE,EAAC,KAAG,GAAGD,IAAEJ,IAAE,GAAGK,IAAEJ,EAAC,GAAEC,EAAC;AAAA,IAAC;AAAC,CAAE;AAAE,IAAI,KAAG;AAAE,SAAS,GAAGE,IAAEL,IAAEC,IAAE;AAAC,MAAGD,KAAE,SAASK,IAAE;AAAC,QAAG,QAAMA;AAAE,aAAOA;AAAE,UAAML,KAAE,OAAOK;AAAE,QAAG,aAAWL;AAAE,aAAO,OAAO,OAAO,OAAO,IAAGK,EAAC,CAAC;AAAE,QAAG,GAAGA,EAAC,GAAE;AAAC,UAAG,aAAWL;AAAE,eAAO,GAAGK,EAAC;AAAE,UAAG,aAAWL;AAAE,eAAO,GAAGK,EAAC;AAAA,IAAC;AAAA,EAAC,EAAEL,EAAC,GAAE,QAAMA,IAAE;AAAC,QAAG,YAAU,OAAOA;AAAE,SAAGA,EAAC;AAAE,QAAG,QAAMA;AAAE,cAAO,GAAGK,IAAEJ,IAAE,CAAC,GAAE,OAAOD,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAK,KAAEA,GAAE,GAAE,GAAGL,EAAC,GAAE,GAAGK,IAAE,IAAG,EAAE;AAAE;AAAA,QAAM,KAAI;AAAS,UAAAJ,KAAE,OAAO,QAAQ,IAAGD,EAAC,GAAEC,KAAE,IAAI,GAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,GAAE,OAAOA,MAAG,OAAO,EAAE,CAAC,CAAC,GAAE,GAAGI,GAAE,GAAEJ,GAAE,GAAEA,GAAE,CAAC;AAAE;AAAA,QAAM;AAAQ,UAAAA,KAAE,GAAGD,EAAC,GAAE,GAAGK,GAAE,GAAEJ,GAAE,GAAEA,GAAE,CAAC;AAAA,MAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,QAAMA,OAAI,GAAGK,IAAEJ,IAAE,CAAC,GAAE,GAAGI,GAAE,GAAEL,EAAC;AAAE;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGK,IAAEJ,IAAE,CAAC,GAAEI,GAAE,EAAE,EAAE,KAAKL,KAAE,IAAE,CAAC;AAAE;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,GAAGK,IAAEJ,IAAE,EAAED,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,EAAAH,cAAa,MAAI,GAAGA,EAAC,GAAEA,KAAEA,GAAE,KAAGA,KAAE,MAAM,QAAQA,EAAC,IAAE,GAAGA,IAAEE,EAAC,IAAE,QAAO,GAAGG,IAAEJ,IAAED,IAAEG,EAAC;AAAC;AAAC,SAAS,GAAGE,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,QAAMA,MAAG,YAAU,OAAOA,MAAG,EAAEA,EAAC,KAAGA,cAAa,IAAEA,KAAE,WAAS,GAAGK,IAAEJ,IAAE,GAAGD,EAAC,EAAE,MAAM;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,UAAO,MAAII,GAAE,KAAG,MAAIA,GAAE,OAAKL,KAAE,GAAGA,IAAE,GAAGA,EAAC,GAAEC,IAAE,GAAE,KAAE,GAAE,KAAGI,GAAE,IAAE,GAAGA,IAAE,IAAGL,EAAC,IAAEA,GAAE,KAAK,GAAGK,GAAE,CAAC,CAAC,GAAE;AAAG;AAAC,IAAI;AAAJ,IAAO,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,MAAII,GAAE;AAAE,WAAM;AAAG,MAAIH,KAAEG,GAAE;AAAE,EAAAA,KAAE,GAAGH,EAAC;AAAE,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAA,KAAE,KAAGC,MAAG,MAAI;AAAE,QAAMC,KAAED,OAAI,KAAG;AAAK,SAAOE,KAAE,cAAY,UAAQF,MAAGE,IAAE,GAAGL,IAAEC,IAAE,QAAMG,KAAEC,KAAE,MAAIH,MAAG,IAAE,KAAG,KAAGE,KAAE,SAAOF,KAAEG,KAAEH,KAAE,KAAK,IAAI,GAAEE,KAAE,IAAI,KAAGC,KAAE,iBAAiB,GAAE;AAAE,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGK,IAAEJ,IAAE,CAAC,GAAEI,KAAEA,GAAE,IAAGJ,KAAE,YAAK,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC,IAAG,WAAW,GAAE,CAACD,IAAE,IAAE,GAAE,KAAGC,GAAE,UAAU,GAAE,IAAE,GAAE,KAAGA,GAAE,UAAU,GAAE,IAAE,GAAE,GAAGI,IAAE,EAAE,GAAE,GAAGA,IAAE,EAAE;AAAE,CAAE;AAAla,IAAoa,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGK,IAAEJ,IAAE,CAAC,GAAEI,KAAEA,GAAE,GAAE,GAAGL,EAAC,GAAE,GAAGK,IAAE,EAAE;AAAE,CAAE;AAAtiB,IAAwiB,KAAG,GAAG,IAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC;AAAG,aAAQM,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,UAAIJ,KAAEG,IAAEF,KAAEF,IAAEG,KAAEJ,GAAEM,EAAC;AAAE,cAAMF,OAAI,GAAGF,IAAEC,IAAE,CAAC,GAAED,KAAEA,GAAE,GAAE,GAAGE,EAAC,GAAE,GAAGF,IAAE,EAAE;AAAA,IAAE;AAAC,CAAE;AAAnrB,IAAqrB,KAAG,GAAG,IAAI,SAASG,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,OAAGK,IAAEJ,IAAE,CAAC,GAAE,GAAGI,GAAE,GAAE,IAAEL,GAAE,MAAM;AAAE,aAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,MAAAD,KAAEI,GAAE,GAAE,GAAGL,GAAEE,EAAC,CAAC,GAAE,GAAGD,IAAE,EAAE;AAAA,EAAC;AAAC,CAAE;AAAl0B,IAAo0B,KAAG,GAAI,SAASI,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,GAAE,GAAE,EAAE,CAAC,GAAE;AAAG,GAAG,EAAE;AAAv4B,IAAy4B,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,OAAKI,KAAE,GAAGA,GAAE,GAAE,EAAE,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,EAAE;AAA79B,IAA+9B,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,GAAE,GAAE,EAAE,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAGA,EAAC,IAAG;AAAC,QAAG,YAAU,OAAOA;AAAE,SAAGA,EAAC;AAAE,QAAG,QAAMA;AAAE,cAAO,GAAGK,IAAEJ,IAAE,CAAC,GAAE,OAAOD,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAK,KAAEA,GAAE,GAAE,GAAGL,EAAC,GAAE,GAAGK,IAAE,IAAG,EAAE;AAAE;AAAA,QAAM,KAAI;AAAS,UAAAJ,KAAE,OAAO,QAAQ,IAAGD,EAAC,GAAEC,KAAE,IAAI,GAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,GAAE,OAAOA,MAAG,OAAO,EAAE,CAAC,CAAC,GAAE,GAAGI,GAAE,GAAEJ,GAAE,GAAEA,GAAE,CAAC;AAAE;AAAA,QAAM;AAAQ,UAAAA,KAAE,GAAGD,EAAC,GAAE,GAAGK,GAAE,GAAEJ,GAAE,GAAEA,GAAE,CAAC;AAAA,MAAC;AAAA,EAAC;AAAC,CAAE;AAA50C,IAA80C,KAAG,GAAI,SAASI,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,GAAE,CAAC,CAAC,GAAE;AAAG,GAAG,EAAE;AAA94C,IAAg5C,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,UAAO,MAAII,GAAE,KAAG,MAAIA,GAAE,OAAKL,KAAE,GAAGA,IAAE,GAAGA,EAAC,GAAEC,IAAE,GAAE,KAAE,GAAE,KAAGI,GAAE,IAAE,GAAGA,IAAE,IAAGL,EAAC,IAAEA,GAAE,KAAK,GAAGK,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,IAAAC,KAAE,GAAGI,IAAEJ,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,SAAGI,GAAE,GAAEL,GAAEC,EAAC,CAAC;AAAE,OAAGI,IAAEJ,EAAC;AAAA,EAAC;AAAC,CAAE;AAA7mD,IAA+mD,KAAG,GAAI,SAASI,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,OAAKI,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,EAAE;AAAhsD,IAAksD,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,GAAE,CAAC,CAAC,GAAE;AAAG,GAAG,EAAE;AAAlwD,IAAowD,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,WAAMI,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,EAAE;AAAt1D,IAAw1D,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,MAAII,GAAE;AAAE,WAAM;AAAG,EAAAA,KAAE,GAAGA,EAAC;AAAE,QAAMH,KAAE,GAAGF,EAAC;AAAE,SAAO,GAAGE,EAAC,GAAE,GAAGF,IAAEE,IAAED,IAAE,CAAC,EAAE,KAAKI,EAAC,GAAE;AAAE,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC;AAAG,aAAQM,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,UAAIJ,KAAEG,IAAEF,KAAEF,IAAEG,KAAEJ,GAAEM,EAAC;AAAE,cAAMF,MAAG,GAAGF,IAAEC,IAAE,EAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,CAAE;AAA7iE,IAA+iE,KAAG,GAAI,SAASC,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,QAAMI,KAAE,GAAGA,EAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,GAAG,EAAE;AAA/nE,IAAioE,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,EAAC,CAAC,GAAE;AAAG,GAAG,EAAE;AAA/rE,IAAisE,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIE,GAAE,MAAI,GAAGA,IAAE,GAAGL,IAAEE,IAAED,IAAE,IAAE,GAAEE,EAAC,GAAE;AAAG,GAAG,EAAE;AAA1wE,IAA4wE,KAAG,GAAI,SAASE,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIE,GAAE,MAAI,GAAGA,IAAE,GAAGL,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,GAAG,EAAE;AAAE,KAAG,IAAI,GAAI,SAASE,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAIE,GAAE;AAAE,WAAM;AAAG,EAAAH,KAAE,GAAG,QAAOA,EAAC;AAAE,MAAIE,KAAE,GAAGJ,EAAC;AAAE,KAAGI,EAAC;AAAE,MAAIE,KAAE,GAAGN,IAAEI,IAAEH,IAAE,CAAC;AAAE,SAAOG,KAAE,GAAGJ,EAAC,GAAE,IAAE,GAAGM,EAAC,MAAIA,KAAE,EAAEA,EAAC,GAAE,GAAGA,IAAE,SAAO,IAAE,GAAGA,EAAC,EAAE,GAAE,GAAGN,IAAEI,IAAEH,IAAEK,EAAC,IAAGA,GAAE,KAAKJ,EAAC,GAAE,GAAGG,IAAEH,IAAEC,EAAC,GAAE;AAAE,GAAI,SAASE,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAM,QAAQH,EAAC;AAAE,aAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI;AAAI,SAAGC,IAAEL,GAAEI,EAAC,GAAEH,IAAEC,IAAEC,EAAC;AAAC,GAAG,IAAE;AAAE,IAAI,KAAG,GAAI,SAASE,IAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIC,GAAE,MAAI,GAAGL,IAAE,GAAGA,EAAC,GAAEI,IAAEH,EAAC,GAAE,GAAGI,IAAEL,KAAE,GAAGA,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,GAAG,EAAE;AAA9F,IAAgG,KAAG,GAAI,SAASE,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,EAAC,CAAC,GAAE;AAAG,GAAG,EAAE;AAA9J,IAAgK,KAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,UAAO,MAAII,GAAE,KAAG,MAAIA,GAAE,OAAKL,KAAE,GAAGA,IAAE,GAAGA,EAAC,GAAEC,IAAE,GAAE,KAAE,GAAE,KAAGI,GAAE,IAAE,GAAGA,IAAE,IAAGL,EAAC,IAAEA,GAAE,KAAK,GAAGK,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC;AAAG,aAAQM,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,UAAIJ,KAAEG,IAAEF,KAAEF,IAAEG,KAAEJ,GAAEM,EAAC;AAAE,cAAMF,OAAI,GAAGF,IAAEC,IAAE,CAAC,GAAE,GAAGD,GAAE,GAAEE,EAAC;AAAA,IAAE;AAAC,CAAE;AAAtY,IAAwY,KAAG,GAAI,SAASC,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,IAAE,GAAGI,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAKA,KAAE,SAASA,IAAE,EAAE,GAAE,GAAGK,IAAEJ,IAAE,CAAC,GAAE,GAAGI,GAAE,GAAEL,EAAC;AAAE,CAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,SAAK,IAAEK,IAAE,KAAK,IAAEL,IAAE,KAAK,IAAE,IAAG,KAAK,IAAE,IAAG,KAAK,eAAa;AAAA,EAAM;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAO,IAAI,GAAGK,IAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,SAAM,CAACC,IAAEC,OAAI;AAAC,QAAG,GAAG,QAAO;AAAC,YAAMG,KAAE,GAAG,IAAI;AAAE,MAAAA,GAAE,EAAEH,EAAC,GAAE,GAAGG,GAAE,GAAEJ,IAAEC,EAAC,GAAED,KAAEI;AAAA,IAAC;AAAM,MAAAJ,KAAE,IAAI,MAAK;AAAA,QAAC,YAAYI,IAAEL,IAAE;AAAC,cAAG,GAAG,QAAO;AAAC,kBAAMC,KAAE,GAAG,IAAI;AAAE,eAAGA,IAAEI,IAAEL,EAAC,GAAEK,KAAEJ;AAAA,UAAC;AAAM,YAAAI,KAAE,IAAI,MAAK;AAAA,cAAC,YAAYA,IAAEL,IAAE;AAAC,qBAAK,IAAE,MAAK,KAAK,IAAE,OAAG,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,GAAG,MAAKK,IAAEL,EAAC;AAAA,cAAC;AAAA,cAAC,QAAO;AAAC,qBAAK,IAAE,MAAK,KAAK,IAAE,OAAG,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,KAAK,KAAG;AAAA,cAAE;AAAA,YAAC,EAAEK,IAAEL,EAAC;AAAE,eAAK,IAAEK,IAAE,KAAK,IAAE,KAAK,EAAE,GAAE,KAAK,IAAE,KAAK,IAAE,IAAG,KAAK,EAAEL,EAAC;AAAA,QAAC;AAAA,QAAC,EAAE,EAAC,IAAGK,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,eAAK,KAAGA;AAAA,QAAC;AAAA,MAAC,EAAEJ,IAAEC,EAAC;AAAE,QAAG;AAAC,YAAMA,KAAE,IAAIG,MAAED,KAAEF,GAAE;AAAE,SAAGF,EAAC,EAAEI,IAAEH,EAAC;AAAE,UAAIE,KAAED;AAAA,IAAC,UAAC;AAAQ,MAAAD,GAAE,EAAE,MAAM,GAAEA,GAAE,IAAE,IAAGA,GAAE,IAAE,IAAG,GAAG,SAAO,OAAK,GAAG,KAAKA,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGE,IAAE;AAAC,SAAO,WAAU;AAAC,OAAG,IAAI;AAAE,UAAML,KAAE,IAAI,MAAK;AAAA,MAAC,cAAa;AAAC,aAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,IAAE,IAAI,MAAK;AAAA,UAAC,cAAa;AAAC,iBAAK,IAAE,CAAC;AAAA,UAAC;AAAA,UAAC,SAAQ;AAAC,mBAAO,KAAK,EAAE;AAAA,UAAM;AAAA,UAAC,MAAK;AAAC,kBAAMK,KAAE,KAAK;AAAE,mBAAO,KAAK,IAAE,CAAC,GAAEA;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAE,OAAG,KAAK,GAAEL,IAAE,GAAGK,EAAC,CAAC,GAAE,GAAGL,IAAEA,GAAE,EAAE,IAAI,CAAC;AAAE,UAAMC,KAAE,IAAI,WAAWD,GAAE,CAAC,GAAEE,KAAEF,GAAE,GAAEG,KAAED,GAAE;AAAO,QAAIE,KAAE;AAAE,aAAQC,KAAE,GAAEA,KAAEF,IAAEE,MAAI;AAAC,YAAML,KAAEE,GAAEG,EAAC;AAAE,MAAAJ,GAAE,IAAID,IAAEI,EAAC,GAAEA,MAAGJ,GAAE;AAAA,IAAM;AAAC,WAAOA,GAAE,IAAE,CAACC,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYI,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,CAAC,GAAE,IAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,SAAO,MAAII,GAAE,MAAI,GAAGL,IAAEC,KAAGI,KAAE,GAAGA,EAAC,OAAK,EAAE,IAAE,SAAOA,EAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,QAAMD,IAAE;AAAC,QAAGA,cAAa,IAAG;AAAC,YAAME,KAAEF,GAAE;AAAG,aAAO,MAAKE,OAAIF,KAAEE,GAAEF,EAAC,GAAE,QAAMA,MAAG,GAAGK,IAAEJ,IAAE,GAAGD,EAAC,EAAE,MAAM;AAAA,IAAG;AAAC,QAAG,MAAM,QAAQA,EAAC;AAAE;AAAA,EAAM;AAAC,KAAGK,IAAEL,IAAEC,EAAC;AAAC,CAAE,CAAC;AAAnS,IAAqS,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAA/T,IAAiU,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAA3W,IAA6W,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,GAAI,SAASI,IAAEL,IAAEC,IAAE;AAAC,UAAO,MAAII,GAAE,KAAG,MAAIA,GAAE,OAAKL,KAAE,GAAGA,IAAE,GAAGA,EAAC,GAAEC,IAAE,GAAE,KAAE,GAAE,KAAGI,GAAE,IAAE,GAAGA,IAAE,IAAGL,EAAC,IAAEA,GAAE,KAAK,GAAGK,GAAE,CAAC,CAAC,GAAE;AAAG,GAAI,SAASA,IAAEL,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,IAAAC,KAAE,GAAGI,IAAEJ,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,SAAGI,GAAE,GAAEL,GAAEC,EAAC,CAAC;AAAE,OAAGI,IAAEJ,EAAC;AAAA,EAAC;AAAC,CAAE,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,EAAE;AAAtnB,IAAwnB,KAAG,CAAC,GAAE,IAAG,EAAE;AAAnoB,IAAqoB,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAA/qB,IAAirB,KAAG,CAAC,CAAC;AAAtrB,IAAwrB,KAAG,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE;AAA3sB,IAA6sB,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,EAAE;AAA92B,IAAg3B,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYI,IAAE;AAAC,UAAMA,IAAE,CAAC;AAAA,EAAC;AAAC;AAA95B,IAAg6B,KAAG,CAAC;AAAp6B,IAAs6B,KAAG,GAAG,IAAE,CAAC;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,SAAS,GAAGA,IAAEL,IAAE;AAAC,KAAGK,IAAE,GAAE,GAAGL,EAAC,GAAE,EAAE;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,KAAGK,IAAE,GAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,KAAGK,IAAE,GAAEL,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAE;AAAC,UAAMA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,MAAK,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAC;AAA/E,IAAiF,KAAG,CAAC,IAAG,EAAC,GAAE,CAAC,EAAC,CAAC;AAA9F,IAAgG,KAAG,CAAC,GAAE,IAAG,GAAE,EAAE;AAA7G,IAA+G,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,SAAS,GAAGA,IAAEL,IAAE;AAAC,KAAGK,IAAE,GAAE,IAAGL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,KAAGK,IAAE,IAAGL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,KAAGK,IAAE,IAAGL,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAE;AAAC,UAAMA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,MAAK,GAAE,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAlF,IAAoF,KAAG,CAAC,MAAK,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,IAAG,CAAC,IAAG,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,EAAE,GAAE,GAAE,IAAG,CAAC,MAAK,IAAG,IAAG,CAAC,IAAG,EAAC,GAAE,CAAC,EAAC,CAAC,GAAE,KAAI,EAAE,GAAE,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,CAAC,IAAG,EAAC,GAAE,CAAC,EAAC,GAAE,EAAE,GAAE,KAAI,IAAG,EAAE,GAAE,IAAG,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,KAAI,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,EAAE;AAAE,GAAG,UAAU,IAAE,GAAG,EAAE;AAAE,IAAI,KAAG,GAAG,IAAG,EAAE;AAAf,IAAiB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA7D,IAA+D,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAC;AAApI,IAAsI,KAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC;AAA9J,IAAgK,KAAG,GAAG,IAAG,EAAE;AAA3K,IAA6K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzN,IAA2N,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvQ,IAAyQ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAC;AAAvW,IAAyW,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,EAAE,CAAC;AAA3gB,IAA6gB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzjB,IAA2jB,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAA5nB,IAA8nB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1qB,IAA4qB,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAA7uB,IAA+uB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA3xB,IAA6xB,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA9yB,IAAgzB,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,GAAE,IAAG,IAAG,EAAE,GAAE,EAAE,CAAC;AAA3H,IAA6H,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzK,IAA2K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,UAAMA,KAAE,GAAG,IAAI;AAAE,WAAO,QAAMA,KAAE,EAAE,IAAEA;AAAA,EAAC;AAAC;AAAlQ,IAAoQ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhT,IAAkT,KAAG,CAAC,GAAE,CAAC;AAAzT,IAA2T,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,EAAE,GAAE,EAAE,CAAC;AAAtZ,IAAwZ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAApc,IAAsc,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAA1d,IAA4d,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAxgB,IAA0gB,KAAG,CAAC,GAAE,IAAG,EAAE;AAArhB,IAAuhB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAnkB,IAAqkB,KAAG,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC;AAAllB,IAAolB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,QAAM,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,QAAM,GAAG,GAAG,MAAK,CAAC,CAAC;AAAA,EAAC;AAAC;AAA1rB,IAA4rB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,GAAG,MAAK,CAAC,CAAC,KAAG;AAAA,EAAE;AAAC;AAAtwB,IAAwwB,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,CAAC;AAA1yB,IAA4yB,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,EAAE;AAA/1B,IAAi2B,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA74B,IAA+4B,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAAh6B,IAAk6B,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,CAAC,GAAE,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,CAAC,GAAE,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,EAAE,CAAC;AAA3E,IAA6E,KAAG,CAAC,GAAE,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAAjH,IAAmH,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,OAAG,MAAK,CAAC;AAAA,EAAC;AAAC;AAAxF,IAA0F,KAAG,CAAC,GAAE,IAAG,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,CAAC,GAAE,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5I,IAA8I,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1L,IAA4L,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAxO,IAA0O,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA3P,IAA6P,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA3Q,IAA6Q,KAAG,CAAC,GAAE,IAAG,EAAE;AAAxR,IAA0R,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA3S,IAA6S,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5I,IAA8I,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAxL,IAA0L,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA3M,IAA6M,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,GAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,CAAC,GAAE,IAAG,EAAE;AAA3G,IAA6G,KAAG,GAAG,WAAU,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIA,KAAE,KAAK;AAAE,UAAML,KAAE,GAAGK,EAAC;AAAE,UAAMJ,KAAE,IAAED;AAAE,WAAOK,KAAE,SAASA,IAAEL,IAAEC,IAAE;AAAC,UAAIC,KAAE;AAAG,YAAMC,KAAE,IAAEH;AAAE,UAAII,KAAE;AAAG,UAAG,QAAMH,IAAE;AAAC,YAAGE;AAAE,iBAAO,GAAG;AAAE,QAAAF,KAAE,CAAC;AAAA,MAAC,WAASA,GAAE,gBAAc,IAAG;AAAC,YAAG,MAAI,IAAEA,GAAE,MAAIE;AAAE,iBAAOF;AAAE,QAAAA,KAAEA,GAAE,EAAE;AAAA,MAAC;AAAM,cAAM,QAAQA,EAAC,IAAEG,KAAE,CAAC,EAAE,IAAE,GAAGH,EAAC,KAAGA,KAAE,CAAC;AAAE,UAAGE,IAAE;AAAC,YAAG,CAACF,GAAE;AAAO,iBAAO,GAAG;AAAE,QAAAG,OAAIA,KAAE,MAAG,GAAGH,EAAC;AAAA,MAAE;AAAM,QAAAG,OAAIA,KAAE,OAAGH,KAAE,GAAGA,EAAC;AAAG,aAAOG,OAAI,KAAG,GAAGH,EAAC,IAAE,EAAEA,IAAE,EAAE,IAAE,KAAGD,MAAG,EAAEC,IAAE,EAAE,IAAG,GAAGI,IAAEL,IAAE,GAAEE,KAAE,IAAI,GAAGD,IAAEC,IAAE,IAAG,MAAM,CAAC,GAAEA;AAAA,IAAC,EAAEG,IAAEL,IAAE,GAAGK,IAAEL,IAAE,CAAC,CAAC,GAAE,CAACC,MAAG,OAAKI,GAAE,KAAG,OAAIA;AAAA,EAAC;AAAC,CAAC;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,CAAC,MAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC,CAAC,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,SAAS,GAAGA,IAAEL,IAAE;AAAC,SAAOA,KAAEA,KAAEA,GAAE,MAAM,IAAE,IAAI,MAAG,WAASK,GAAE,qBAAmB,GAAGL,IAAE,GAAE,GAAGK,GAAE,kBAAkB,CAAC,IAAE,WAASA,GAAE,sBAAoB,GAAGL,IAAE,CAAC,GAAE,WAASK,GAAE,aAAW,GAAGL,IAAE,GAAEK,GAAE,UAAU,IAAE,gBAAeA,MAAG,GAAGL,IAAE,CAAC,GAAE,WAASK,GAAE,iBAAe,GAAGL,IAAE,GAAEK,GAAE,cAAc,IAAE,oBAAmBA,MAAG,GAAGL,IAAE,CAAC,GAAE,WAASK,GAAE,oBAAkB,GAAGL,IAAE,GAAEK,GAAE,iBAAiB,IAAE,uBAAsBA,MAAG,GAAGL,IAAE,CAAC,GAAE,WAASK,GAAE,mBAAiB,GAAGL,IAAE,GAAEK,GAAE,gBAAgB,IAAE,sBAAqBA,MAAG,GAAGL,IAAE,CAAC,GAAEA;AAAC;AAAC,SAAS,GAAGK,IAAEL,KAAE,IAAGC,KAAE,IAAG;AAAC,SAAM,EAAC,YAAWI,GAAE,IAAK,CAAAA,QAAI,EAAC,OAAM,GAAG,GAAGA,IAAE,CAAC,GAAE,CAAC,KAAG,IAAG,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,cAAa,GAAGA,IAAE,CAAC,KAAG,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,GAAE,EAAG,GAAE,WAAUL,IAAE,UAASC,GAAC;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAp01C,MAAAK,KAAA;AAAq01C,MAAIV,KAAE,GAAGK,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEJ,KAAE,GAAGI,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEH,KAAE,GAAGG,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEF,KAAE,GAAGE,IAAE,GAAE,IAAG,GAAG,CAAC;AAAE,QAAMD,KAAE,EAAC,YAAW,CAAC,GAAE,WAAU,CAAC,EAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEL,GAAE,QAAOK;AAAI,IAAAD,GAAE,WAAW,KAAK,EAAC,OAAMJ,GAAEK,EAAC,GAAE,OAAMJ,GAAEI,EAAC,KAAG,IAAG,cAAaH,GAAEG,EAAC,KAAG,IAAG,aAAYF,GAAEE,EAAC,KAAG,GAAE,CAAC;AAAE,OAAIL,MAAEU,MAAA,GAAGL,IAAE,IAAG,CAAC,MAAT,gBAAAK,IAAY,SAAON,GAAE,cAAY,EAAC,SAAQ,GAAGJ,IAAE,CAAC,KAAG,GAAE,SAAQ,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,QAAO,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,EAAC,KAAG,QAAGK,IAAE,IAAG,CAAC,MAAT,mBAAY,IAAI;AAAO,eAAUL,MAAK,GAAGK,IAAE,IAAG,CAAC,EAAE,EAAE;AAAE,MAAAD,GAAE,UAAU,KAAK,EAAC,GAAE,GAAGJ,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAG,GAAGA,IAAE,CAAC,CAAC,KAAG,GAAE,CAAC;AAAE,SAAOI;AAAC;AAAC,SAAS,GAAGC,IAAE;AAAC,QAAML,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAGI,IAAE,IAAG,CAAC;AAAE,IAAAL,GAAE,KAAK,EAAC,GAAE,GAAGC,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,QAAML,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAGI,IAAE,IAAG,CAAC;AAAE,IAAAL,GAAE,KAAK,EAAC,GAAE,GAAGC,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,SAAO,MAAM,KAAKA,IAAG,CAAAA,OAAGA,KAAE,MAAIA,KAAE,MAAIA,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,MAAGK,GAAE,WAASL,GAAE;AAAO,UAAM,MAAM,2EAA2EK,GAAE,MAAM,QAAQL,GAAE,MAAM,IAAI;AAAE,MAAIC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEC,GAAE,QAAOD;AAAI,IAAAH,MAAGI,GAAED,EAAC,IAAEJ,GAAEI,EAAC,GAAEF,MAAGG,GAAED,EAAC,IAAEC,GAAED,EAAC,GAAED,MAAGH,GAAEI,EAAC,IAAEJ,GAAEI,EAAC;AAAE,MAAGF,MAAG,KAAGC,MAAG;AAAE,UAAM,MAAM,4DAA4D;AAAE,SAAOF,KAAE,KAAK,KAAKC,KAAEC,EAAC;AAAC;AAAC,IAAI;AAAG,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE,GAAE,GAAG,SAAS,IAAE;AAAE,IAAM,KAAG,IAAI,WAAW,CAAC,GAAE,IAAG,KAAI,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,KAAI,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,IAAG,KAAI,IAAG,EAAE,CAAC;AAAE,eAAe,KAAI;AAAC,MAAG,WAAS;AAAG,QAAG;AAAC,YAAM,YAAY,YAAY,EAAE,GAAE,KAAG;AAAA,IAAE,QAAM;AAAC,WAAG;AAAA,IAAE;AAAC,SAAO;AAAE;AAAC,eAAe,GAAGE,IAAEL,KAAE,IAAG;AAAC,QAAMC,KAAE,MAAM,GAAG,IAAE,kBAAgB;AAAuB,SAAM,EAAC,gBAAe,GAAGD,EAAC,IAAIK,EAAC,IAAIJ,EAAC,OAAM,gBAAe,GAAGD,EAAC,IAAIK,EAAC,IAAIJ,EAAC,QAAO;AAAC;AAAC,IAAI,KAAG,MAAK;AAAC;AAAE,SAAS,KAAI;AAAC,MAAII,KAAE;AAAU,SAAM,eAAa,OAAO,oBAAkB,CAAC,SAASA,KAAE,WAAU;AAAC,YAAOA,KAAEA,GAAE,WAAW,SAAS,QAAQ,KAAG,CAACA,GAAE,SAAS,QAAQ;AAAA,EAAC,EAAEA,EAAC,KAAG,CAAC,GAAGA,KAAEA,GAAE,UAAU,MAAM,0BAA0B,MAAIA,GAAE,UAAQ,KAAG,OAAOA,GAAE,CAAC,CAAC,KAAG;AAAI;AAAC,eAAe,GAAGA,IAAE;AAAC,MAAG,cAAY,OAAO,eAAc;AAAC,UAAML,KAAE,SAAS,cAAc,QAAQ;AAAE,WAAOA,GAAE,MAAIK,GAAE,SAAS,GAAEL,GAAE,cAAY,aAAY,IAAI,QAAS,CAACK,IAAEJ,OAAI;AAAC,MAAAD,GAAE,iBAAiB,QAAQ,MAAI;AAAC,QAAAK,GAAE;AAAA,MAAC,GAAG,KAAE,GAAEL,GAAE,iBAAiB,SAAS,CAAAK,OAAG;AAAC,QAAAJ,GAAEI,EAAC;AAAA,MAAC,GAAG,KAAE,GAAE,SAAS,KAAK,YAAYL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,gBAAcK,GAAE,SAAS,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,WAASA,GAAE,aAAW,CAACA,GAAE,YAAWA,GAAE,WAAW,IAAE,WAASA,GAAE,eAAa,CAACA,GAAE,cAAaA,GAAE,aAAa,IAAE,WAASA,GAAE,eAAa,CAACA,GAAE,cAAaA,GAAE,aAAa,IAAE,CAACA,GAAE,OAAMA,GAAE,MAAM;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,EAAAI,GAAE,KAAG,QAAQ,MAAM,mHAAmH,GAAEJ,GAAED,KAAEK,GAAE,EAAE,gBAAgBL,EAAC,CAAC,GAAEK,GAAE,EAAE,MAAML,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,MAAG,CAACI,GAAE,EAAE;AAAO,UAAM,MAAM,8BAA8B;AAAE,MAAGJ,KAAEI,GAAE,EAAE,qBAAqBJ,EAAC,IAAEI,GAAE,EAAE,qBAAqB,GAAE,EAAEJ,KAAEI,GAAE,EAAE,OAAO,WAAW,QAAQ,KAAGA,GAAE,EAAE,OAAO,WAAW,OAAO;AAAG,UAAM,MAAM,0HAA0H;AAAE,EAAAA,GAAE,EAAE,uCAAqCJ,GAAE,YAAYA,GAAE,qBAAoB,IAAE,GAAEA,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcD,EAAC,GAAEK,GAAE,EAAE,uCAAqCJ,GAAE,YAAYA,GAAE,qBAAoB,KAAE;AAAE,QAAK,CAACC,IAAEC,EAAC,IAAE,GAAGH,EAAC;AAAE,SAAM,CAACK,GAAE,KAAGH,OAAIG,GAAE,EAAE,OAAO,SAAOF,OAAIE,GAAE,EAAE,OAAO,WAASA,GAAE,EAAE,OAAO,QAAMH,IAAEG,GAAE,EAAE,OAAO,SAAOF,KAAG,CAACD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGE,IAAEL,IAAEC,IAAE;AAAC,EAAAI,GAAE,KAAG,QAAQ,MAAM,mHAAmH;AAAE,QAAMH,KAAE,IAAI,YAAYF,GAAE,MAAM;AAAE,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,IAAAC,GAAED,EAAC,IAAEI,GAAE,EAAE,gBAAgBL,GAAEC,EAAC,CAAC;AAAE,EAAAD,KAAEK,GAAE,EAAE,QAAQ,IAAEH,GAAE,MAAM,GAAEG,GAAE,EAAE,QAAQ,IAAIH,IAAEF,MAAG,CAAC,GAAEC,GAAED,EAAC;AAAE,aAAUA,MAAKE;AAAE,IAAAG,GAAE,EAAE,MAAML,EAAC;AAAE,EAAAK,GAAE,EAAE,MAAML,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAE;AAAC,EAAAI,GAAE,EAAE,kBAAgBA,GAAE,EAAE,mBAAiB,CAAC,GAAEA,GAAE,EAAE,gBAAgBL,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,EAAAG,GAAE,EAAE,kBAAgBA,GAAE,EAAE,mBAAiB,CAAC,GAAEA,GAAE,EAAE,gBAAgBL,EAAC,IAAE,CAACK,IAAEL,IAAEG,OAAI;AAAC,IAAAH,MAAGC,GAAEC,IAAEC,EAAC,GAAED,KAAE,CAAC,KAAGA,GAAE,KAAKG,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,iBAAe,SAASA,IAAE;AAAC,SAAO,GAAG,UAASA,EAAC;AAAC,GAAE,GAAG,eAAa,SAASA,IAAE;AAAC,SAAO,GAAG,QAAOA,EAAC;AAAC,GAAE,GAAG,4BAA0B,SAASA,IAAE;AAAC,SAAO,GAAG,sBAAqBA,EAAC;AAAC,GAAE,GAAG,gBAAc,SAASA,IAAE;AAAC,SAAO,GAAG,SAAQA,EAAC;AAAC,GAAE,GAAG,gBAAc,SAASA,IAAE;AAAC,SAAO,GAAG,SAAQA,EAAC;AAAC,GAAE,GAAG,kBAAgB,WAAU;AAAC,SAAO,GAAG;AAAC;AAAE,eAAe,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAOG,KAAE,OAAM,OAAMA,IAAEL,IAAEC,IAAEC,IAAEC,OAAI;AAAC,QAAGH,MAAG,MAAM,GAAGA,EAAC,GAAE,CAAC,KAAK;AAAc,YAAM,MAAM,wBAAwB;AAAE,QAAGC,OAAI,MAAM,GAAGA,EAAC,GAAE,CAAC,KAAK;AAAe,YAAM,MAAM,wBAAwB;AAAE,WAAO,KAAK,UAAQE,QAAKH,KAAE,KAAK,QAAQ,aAAWG,GAAE,YAAWA,GAAE,wBAAsBH,GAAE,sBAAoBG,GAAE,uBAAsBA,KAAE,MAAM,KAAK,cAAc,KAAK,UAAQA,EAAC,GAAE,KAAK,gBAAc,KAAK,SAAO,QAAO,IAAIE,GAAEF,IAAED,EAAC;AAAA,EAAC,GAAGG,IAAEJ,GAAE,gBAAeA,GAAE,iBAAgBD,IAAE,EAAC,YAAW,CAAAK,OAAGA,GAAE,SAAS,OAAO,IAAEJ,GAAE,eAAe,SAAS,IAAEA,GAAE,mBAAiBI,GAAE,SAAS,OAAO,IAAEJ,GAAE,gBAAgB,SAAS,IAAEI,GAAC,CAAC,GAAE,MAAMA,GAAE,EAAEH,EAAC,GAAEG;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,QAAMC,KAAE,GAAGI,GAAE,aAAY,IAAG,CAAC,KAAG,IAAI;AAAG,cAAU,OAAOL,MAAG,GAAGC,IAAE,GAAE,GAAGD,EAAC,CAAC,GAAE,GAAGC,IAAE,CAAC,KAAGD,cAAa,eAAa,GAAGC,IAAE,GAAE,GAAGD,IAAE,OAAG,KAAE,CAAC,GAAE,GAAGC,IAAE,CAAC,IAAG,GAAGI,GAAE,aAAY,GAAE,GAAEJ,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,MAAG;AAAC,UAAML,KAAEK,GAAE,EAAE;AAAO,QAAG,MAAIL;AAAE,YAAM,MAAMK,GAAE,EAAE,CAAC,EAAE,OAAO;AAAE,QAAGL,KAAE;AAAE,YAAM,MAAM,kCAAgCK,GAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,OAAQ,EAAE,KAAK,IAAI,CAAC;AAAA,EAAC,UAAC;AAAQ,IAAAA,GAAE,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,EAAAK,GAAE,IAAE,KAAK,IAAIA,GAAE,GAAEL,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAE;AAAC,EAAAK,GAAE,IAAE,IAAI,MAAG,GAAGA,GAAE,GAAE,uBAAuB,GAAE,GAAGA,GAAE,GAAE,aAAa,GAAE,GAAGA,GAAE,GAAE,wBAAwB,GAAE,GAAGL,IAAE,aAAa,GAAE,GAAGA,IAAEK,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,KAAGK,GAAE,GAAEL,EAAC,GAAE,GAAGK,GAAE,GAAEL,KAAE,aAAa;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,EAAAA,GAAE,EAAE,gBAAgB,MAAG,eAAcA,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,IAAEA,IAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,EAAE,sBAAsB,KAAE;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,KAAE,MAAG;AAAjvgD,QAAAU,KAAA,YAAAK,KAAA;AAAkvgD,QAAGf,IAAE;AAAC,YAAMA,KAAEK,GAAE,eAAa,CAAC;AAAE,YAAGK,MAAAL,GAAE,gBAAF,gBAAAK,IAAe,uBAAkB,KAAAL,GAAE,gBAAF,mBAAe;AAAe,cAAM,MAAM,6EAA6E;AAAE,UAAG,IAAE,QAAG,KAAK,aAAY,IAAG,CAAC,MAAxB,mBAA2B,UAAK,QAAG,KAAK,aAAY,IAAG,CAAC,MAAxB,mBAA2B,UAAKU,MAAAV,GAAE,gBAAF,gBAAAU,IAAe,uBAAkB,KAAAV,GAAE,gBAAF,mBAAe;AAAgB,cAAM,MAAM,+EAA+E;AAAE,UAAG,SAASA,IAAEL,IAAE;AAAC,YAAIC,KAAE,GAAGI,GAAE,aAAY,IAAG,CAAC;AAAE,YAAG,CAACJ,IAAE;AAAC,cAAIC,KAAED,KAAE,IAAI,MAAGE,KAAE,IAAI;AAAG,aAAGD,IAAE,GAAE,IAAGC,EAAC;AAAA,QAAC;AAAC,sBAAaH,OAAI,UAAQA,GAAE,YAAUA,KAAEC,IAAEC,KAAE,IAAI,MAAG,GAAGF,IAAE,GAAE,IAAGE,EAAC,MAAIF,KAAEC,IAAEC,KAAE,IAAI,MAAG,GAAGF,IAAE,GAAE,IAAGE,EAAC,KAAI,GAAGG,GAAE,aAAY,GAAE,GAAEJ,EAAC;AAAA,MAAC,EAAE,MAAKD,EAAC,GAAEA,GAAE;AAAe,eAAO,MAAMA,GAAE,eAAe,SAAS,CAAC,EAAE,KAAM,CAAAK,OAAG;AAAC,cAAGA,GAAE;AAAG,mBAAOA,GAAE,YAAY;AAAE,gBAAM,MAAM,0BAA0BL,GAAE,cAAc,KAAKK,GAAE,MAAM,GAAG;AAAA,QAAC,CAAE,EAAE,KAAM,CAAAA,OAAG;AAAC,cAAG;AAAC,iBAAK,EAAE,EAAE,UAAU,YAAY;AAAA,UAAC,QAAM;AAAA,UAAC;AAAC,eAAK,EAAE,EAAE,kBAAkB,KAAI,aAAY,IAAI,WAAWA,EAAC,GAAE,MAAG,OAAG,KAAE,GAAE,GAAG,MAAK,YAAY,GAAE,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,QAAC,CAAE;AAAE,UAAGL,GAAE,4BAA4B;AAAW,WAAG,MAAKA,GAAE,gBAAgB;AAAA,eAAUA,GAAE;AAAiB,eAAO,eAAeK,IAAE;AAAC,gBAAML,KAAE,CAAC;AAAE,mBAAQC,KAAE,OAAI;AAAC,kBAAK,EAAC,MAAKC,IAAE,OAAMC,GAAC,IAAE,MAAME,GAAE,KAAK;AAAE,gBAAGH;AAAE;AAAM,YAAAF,GAAE,KAAKG,EAAC,GAAEF,MAAGE,GAAE;AAAA,UAAM;AAAC,cAAG,MAAIH,GAAE;AAAO,mBAAO,IAAI,WAAW,CAAC;AAAE,cAAG,MAAIA,GAAE;AAAO,mBAAOA,GAAE,CAAC;AAAE,UAAAK,KAAE,IAAI,WAAWJ,EAAC,GAAEA,KAAE;AAAE,qBAAUC,MAAKF;AAAE,YAAAK,GAAE,IAAIH,IAAED,EAAC,GAAEA,MAAGC,GAAE;AAAO,iBAAOG;AAAA,QAAC,EAAEL,GAAE,gBAAgB,EAAE,KAAM,CAAAK,OAAG;AAAC,aAAG,MAAKA,EAAC,GAAE,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,QAAC,CAAE;AAAA,IAAC;AAAC,WAAO,KAAK,EAAE,GAAE,KAAK,EAAE,GAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAIA;AAAE,QAAG,KAAK,EAAE,GAAI,CAAAL,OAAG;AAAC,MAAAK,KAAE,GAAGL,EAAC;AAAA,IAAC,CAAE,GAAE,CAACK;AAAE,YAAM,MAAM,0CAA0C;AAAE,WAAOA;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEL,IAAE;AAAC,SAAK,EAAE,oBAAqB,CAACK,IAAEL,OAAI;AAAC,WAAK,EAAE,KAAK,MAAMA,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,GAAG,GAAE,KAAK,EAAE,SAASK,IAAEL,EAAC,GAAE,KAAK,IAAE,QAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,EAAE,iBAAiB,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,IAAE,QAAO,KAAK,EAAE,WAAW;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGK,IAAEL,IAAE;AAAC,MAAG,CAACK;AAAE,UAAM,MAAM,6CAA6CL,EAAC,EAAE;AAAE,SAAOK;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,SAASL,IAAEC,IAAE;AAAC,EAAAD,KAAEA,GAAE,MAAM,GAAG;AAAE,MAAIE,IAAEC,KAAE;AAAE,EAAAH,GAAE,CAAC,KAAIG,MAAG,WAASA,GAAE,cAAYA,GAAE,WAAW,SAAOH,GAAE,CAAC,CAAC;AAAE,SAAKA,GAAE,WAASE,KAAEF,GAAE,MAAM;AAAI,IAAAA,GAAE,UAAQ,WAASC,KAAEE,KAAEA,GAAED,EAAC,KAAGC,GAAED,EAAC,MAAI,OAAO,UAAUA,EAAC,IAAEC,GAAED,EAAC,IAAEC,GAAED,EAAC,IAAE,CAAC,IAAEC,GAAED,EAAC,IAAED;AAAC,EAAE,cAAa,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAEG,IAAE,KAAK,IAAEL,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,EAAE,gBAAgB,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,kBAAkB,KAAK,CAAC,GAAE,KAAK,EAAE,aAAa,KAAK,CAAC,GAAE,KAAK,EAAE,aAAa,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGG,IAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAEG,GAAE;AAAE,MAAGJ,KAAE,GAAGC,GAAE,aAAaD,EAAC,GAAE,+BAA+B,GAAEC,GAAE,aAAaD,IAAED,EAAC,GAAEE,GAAE,cAAcD,EAAC,GAAE,CAACC,GAAE,mBAAmBD,IAAEC,GAAE,cAAc;AAAE,UAAM,MAAM,mCAAmCA,GAAE,iBAAiBD,EAAC,CAAC,EAAE;AAAE,SAAOC,GAAE,aAAaG,GAAE,GAAEJ,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAE;AAAC,QAAMC,KAAEI,GAAE,GAAEH,KAAE,GAAGD,GAAE,kBAAkB,GAAE,+BAA+B;AAAE,EAAAA,GAAE,gBAAgBC,EAAC;AAAE,QAAMC,KAAE,GAAGF,GAAE,aAAa,GAAE,yBAAyB;AAAE,EAAAA,GAAE,WAAWA,GAAE,cAAaE,EAAC,GAAEF,GAAE,wBAAwBI,GAAE,CAAC,GAAEJ,GAAE,oBAAoBI,GAAE,GAAE,GAAEJ,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAa,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,EAAE,CAAC,GAAEA,GAAE,WAAW;AAAE,QAAMG,KAAE,GAAGH,GAAE,aAAa,GAAE,yBAAyB;AAAE,SAAOA,GAAE,WAAWA,GAAE,cAAaG,EAAC,GAAEH,GAAE,wBAAwBI,GAAE,CAAC,GAAEJ,GAAE,oBAAoBI,GAAE,GAAE,GAAEJ,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAaD,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEC,GAAE,WAAW,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,GAAEA,GAAE,gBAAgB,IAAI,GAAE,IAAI,GAAGA,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGC,IAAEL,IAAE;AAAC,MAAGK,GAAE,GAAE;AAAC,QAAGL,OAAIK,GAAE;AAAE,YAAM,MAAM,2CAA2C;AAAA,EAAC;AAAM,IAAAA,GAAE,IAAEL;AAAC;AAAC,SAAS,GAAGK,IAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGG,IAAEL,EAAC,GAAEK,GAAE,MAAIA,GAAE,EAAE,GAAEA,GAAE,EAAE,IAAGJ,MAAGI,GAAE,MAAIA,GAAE,IAAE,GAAGA,IAAE,IAAE,IAAGJ,KAAEI,GAAE,MAAIA,GAAE,MAAIA,GAAE,IAAE,GAAGA,IAAE,KAAE,IAAGJ,KAAEI,GAAE,IAAGL,GAAE,WAAWK,GAAE,CAAC,GAAEJ,GAAE,KAAK,GAAEI,GAAE,EAAE,GAAEA,KAAEH,GAAE,GAAED,GAAE,EAAE,gBAAgB,IAAI,GAAEI;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,SAAO,GAAGI,IAAEL,EAAC,GAAEK,KAAE,GAAGL,GAAE,cAAc,GAAE,0BAA0B,GAAEA,GAAE,YAAYA,GAAE,YAAWK,EAAC,GAAEL,GAAE,cAAcA,GAAE,YAAWA,GAAE,gBAAeA,GAAE,aAAa,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,gBAAeA,GAAE,aAAa,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,oBAAmBC,MAAGD,GAAE,MAAM,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,oBAAmBC,MAAGD,GAAE,MAAM,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEK;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,KAAGI,IAAEL,EAAC,GAAEK,GAAE,MAAIA,GAAE,IAAE,GAAGL,GAAE,kBAAkB,GAAE,8BAA8B,IAAGA,GAAE,gBAAgBA,GAAE,aAAYK,GAAE,CAAC,GAAEL,GAAE,qBAAqBA,GAAE,aAAYA,GAAE,mBAAkBA,GAAE,YAAWC,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGI,IAAE;AAA12oD,MAAAK;AAA22oD,GAAAA,MAAAL,GAAE,MAAF,gBAAAK,IAAK,gBAAgBL,GAAE,EAAE,aAAY;AAAK;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAmK;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,QAAG,KAAK,IAAE,GAAGA,GAAE,cAAc,GAAE,gCAAgC,GAAE,KAAK,KAAG,GAAG,MAAK,qKAAoKA,GAAE,aAAa,GAAE,KAAK,KAAG,GAAG,MAAK,KAAK,EAAE,GAAEA,GAAE,eAAe,GAAEA,GAAE,YAAY,KAAK,CAAC,GAAE,CAACA,GAAE,oBAAoB,KAAK,GAAEA,GAAE,WAAW;AAAE,YAAM,MAAM,iCAAiCA,GAAE,kBAAkB,KAAK,CAAC,CAAC,EAAE;AAAE,SAAK,IAAEA,GAAE,kBAAkB,KAAK,GAAE,SAAS,GAAE,KAAK,IAAEA,GAAE,kBAAkB,KAAK,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAG,KAAK,GAAE;AAAC,YAAMA,KAAE,KAAK;AAAE,MAAAA,GAAE,cAAc,KAAK,CAAC,GAAEA,GAAE,aAAa,KAAK,EAAE,GAAEA,GAAE,aAAa,KAAK,EAAE;AAAA,IAAC;AAAC,SAAK,KAAG,KAAK,EAAE,kBAAkB,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,MAAM,GAAE,KAAK,KAAG,KAAK,EAAE,MAAM;AAAA,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAgd;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,IAAEA,GAAE,MAAM,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,IAAEA,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,SAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,mBAAmB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,qBAAqB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,aAAa,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAA1jC,IAA4jC,KAAG,cAAc,GAAE;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAmjB;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,EAAC,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,SAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,gBAAgB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,gBAAgB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,aAAa,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAEL,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAE,aAAOK,GAAE,EAAE,KAAM,CAAAA,OAAGA,cAAa,UAAW;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAGA,cAAa,YAAa;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAG,eAAa,OAAO,gBAAcA,cAAa,YAAa;AAAA,IAAE;AAAQ,YAAM,MAAM,0BAA0BL,EAAC,EAAE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAE,GAAGK,IAAE,CAAC;AAAE,MAAG,CAACL,IAAE;AAAC,QAAGA,KAAE,GAAGK,IAAE,CAAC;AAAE,MAAAL,KAAE,IAAI,aAAaA,EAAC,EAAE,IAAK,CAAAK,OAAGA,KAAE,GAAI;AAAA,SAAM;AAAC,MAAAL,KAAE,IAAI,aAAaK,GAAE,QAAMA,GAAE,MAAM;AAAE,YAAMH,KAAE,GAAGG,EAAC;AAAE,UAAIJ,KAAE,GAAGI,EAAC;AAAE,UAAG,GAAGJ,IAAEC,IAAE,GAAGG,EAAC,CAAC,GAAE,kEAAkE,MAAM,GAAG,EAAE,SAAS,UAAU,QAAQ,KAAG,UAAU,UAAU,SAAS,KAAK,KAAG,gBAAe,KAAK,UAAS;AAAC,QAAAJ,KAAE,IAAI,aAAaI,GAAE,QAAMA,GAAE,SAAO,CAAC,GAAEH,GAAE,WAAW,GAAE,GAAEG,GAAE,OAAMA,GAAE,QAAOH,GAAE,MAAKA,GAAE,OAAMD,EAAC;AAAE,iBAAQI,KAAE,GAAEH,KAAE,GAAEG,KAAEL,GAAE,QAAO,EAAEK,IAAEH,MAAG;AAAE,UAAAF,GAAEK,EAAC,IAAEJ,GAAEC,EAAC;AAAA,MAAC;AAAM,QAAAA,GAAE,WAAW,GAAE,GAAEG,GAAE,OAAMA,GAAE,QAAOH,GAAE,KAAIA,GAAE,OAAMF,EAAC;AAAA,IAAC;AAAC,IAAAK,GAAE,EAAE,KAAKL,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAE,GAAGK,IAAE,CAAC;AAAE,MAAG,CAACL,IAAE;AAAC,UAAMC,KAAE,GAAGI,EAAC;AAAE,IAAAL,KAAE,GAAGK,EAAC;AAAE,UAAMH,KAAE,GAAGG,EAAC,GAAEF,KAAE,GAAGE,EAAC;AAAE,IAAAJ,GAAE,WAAWA,GAAE,YAAW,GAAEE,IAAEE,GAAE,OAAMA,GAAE,QAAO,GAAEJ,GAAE,KAAIA,GAAE,OAAMC,EAAC,GAAE,GAAGG,EAAC;AAAA,EAAC;AAAC,SAAOL;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,UAAM,MAAM,oGAAoG;AAAE,SAAOA,GAAE,MAAIA,GAAE,IAAE,GAAGA,GAAE,OAAO,WAAW,QAAQ,GAAE,yFAAyF,IAAGA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAGA,KAAE,GAAGA,EAAC,GAAE,CAAC;AAAG,QAAGA,GAAE,aAAa,wBAAwB,KAAGA,GAAE,aAAa,0BAA0B,KAAGA,GAAE,aAAa,iBAAiB;AAAE,WAAGA,GAAE;AAAA,SAAS;AAAC,UAAG,CAACA,GAAE,aAAa,6BAA6B;AAAE,cAAM,MAAM,iEAAiE;AAAE,WAAGA,GAAE;AAAA,IAAI;AAAC,SAAO;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAML,KAAE,GAAGK,EAAC;AAAE,EAAAL,GAAE,SAAS,GAAE,GAAEK,GAAE,OAAMA,GAAE,MAAM,GAAEL,GAAE,cAAcA,GAAE,QAAQ;AAAE,MAAIC,KAAE,GAAGI,IAAE,CAAC;AAAE,SAAOJ,OAAIA,KAAE,GAAG,GAAGI,EAAC,GAAEL,IAAEK,GAAE,IAAEL,GAAE,SAAOA,GAAE,OAAO,GAAEK,GAAE,EAAE,KAAKJ,EAAC,GAAEI,GAAE,IAAE,OAAIL,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,EAAAA,GAAE,EAAE,YAAYA,GAAE,EAAE,YAAW,IAAI;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,SAAK,IAAED,IAAE,KAAK,IAAEL,IAAE,KAAK,IAAEC,IAAE,KAAK,SAAOC,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOE,IAAE,KAAK,MAAI,MAAI,EAAE,MAAI,QAAQ,MAAM,2FAA2F;AAAA,EAAE;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,YAAON,KAAE,GAAGK,KAAE,MAAK,CAAC,OAAKL,KAAE,GAAGK,EAAC,GAAEL,KAAE,IAAI,WAAWA,GAAE,IAAK,CAAAK,OAAG,MAAIA,EAAE,CAAC,GAAEA,GAAE,EAAE,KAAKL,EAAC,IAAGA;AAAE,QAAIK,IAAEL;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMK,KAAE,CAAC;AAAE,eAAUL,MAAK,KAAK,GAAE;AAAC,UAAIC;AAAE,UAAGD,cAAa;AAAW,QAAAC,KAAE,IAAI,WAAWD,EAAC;AAAA,eAAUA,cAAa;AAAa,QAAAC,KAAE,IAAI,aAAaD,EAAC;AAAA,WAAM;AAAC,YAAG,EAAEA,cAAa;AAAc,gBAAM,MAAM,0BAA0BA,EAAC,EAAE;AAAE;AAAC,gBAAMK,KAAE,GAAG,IAAI,GAAEL,KAAE,GAAG,IAAI;AAAE,UAAAK,GAAE,cAAcA,GAAE,QAAQ,GAAEJ,KAAE,GAAGD,IAAEK,IAAE,KAAK,IAAEA,GAAE,SAAOA,GAAE,OAAO,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,EAAC;AAAE,gBAAMC,KAAE,GAAG,IAAI;AAAE,UAAAG,GAAE,WAAWA,GAAE,YAAW,GAAEH,IAAE,KAAK,OAAM,KAAK,QAAO,GAAEG,GAAE,KAAIA,GAAE,OAAM,IAAI,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAE,GAAGL,IAAEK,IAAEJ,EAAC,GAAE,GAAGD,IAAEK,IAAE,OAAI,MAAI;AAAC,eAAG,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,GAAG,IAAI;AAAA,UAAC,CAAE,GAAE,GAAGL,EAAC,GAAE,GAAG,IAAI;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAK,GAAE,KAAKJ,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,GAAGI,IAAE,KAAK,GAAE,KAAK,EAAE,GAAE,KAAK,QAAO,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,GAAG,IAAI,EAAE,cAAc,GAAG,MAAK,CAAC,CAAC,GAAE,KAAG;AAAA,EAAE;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,oBAAkB,GAAG,UAAU,GAAE,GAAG,UAAU,oBAAkB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,GAAE,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,gBAAc,GAAG,UAAU;AAAG,IAAI,KAAG;AAAI,IAAM,KAAG,EAAC,OAAM,SAAQ,WAAU,GAAE,QAAO,EAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,GAAG,IAAG,YAAWA,KAAEA,MAAG,CAAC,GAAG,OAAM,GAAGA,GAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,SAAOK,cAAa,WAASA,GAAEL,EAAC,IAAEK;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,SAAO,KAAK,IAAI,KAAK,IAAID,IAAEC,EAAC,GAAE,KAAK,IAAI,KAAK,IAAID,IAAEC,EAAC,GAAEI,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,CAACA,GAAE;AAAE,UAAM,MAAM,oEAAoE;AAAE,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,CAACA,GAAE;AAAE,UAAM,MAAM,kEAAkE;AAAE,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAC,MAAGD,GAAE,EAAE;AAAE,IAAAC,GAAED,GAAE,EAAE,CAAC;AAAA,OAAM;AAAC,UAAME,KAAEF,GAAE,GAAG,IAAEA,GAAE,GAAG,IAAEA,GAAE,GAAG;AAAE,IAAAK,GAAE,IAAEA,GAAE,KAAG,IAAI;AAAG,UAAMF,KAAE,GAAGE,EAAC;AAAE,IAAAJ,IAAGI,KAAE,IAAI,GAAG,CAACH,EAAC,GAAEF,GAAE,GAAE,OAAGG,GAAE,QAAOE,GAAE,GAAEL,GAAE,OAAMA,GAAE,MAAM,GAAG,EAAE,CAAC,GAAEK,GAAE,MAAM;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,SAASE,IAAE;AAAC,WAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAA,EAAC,EAAEA,EAAC,GAAED,KAAE,GAAGC,EAAC,GAAEC,KAAE,MAAM,QAAQL,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA;AAAE,KAAGE,IAAEC,IAAE,MAAI,MAAI;AAAC,KAAC,SAASC,IAAEL,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAEE,GAAE;AAAE,UAAGF,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWH,EAAC,GAAEG,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWE,GAAE,CAAC,GAAEF,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcF,EAAC,GAAEI,GAAE,KAAG,SAASA,IAAEL,IAAE;AAAC,YAAGK,OAAIL;AAAE,iBAAM;AAAG,QAAAK,KAAEA,GAAE,QAAQ,GAAEL,KAAEA,GAAE,QAAQ;AAAE,mBAAS,CAACE,IAAEC,EAAC,KAAIE,IAAE;AAAC,UAAAA,KAAEH;AAAE,gBAAME,KAAED;AAAE,cAAIF,KAAED,GAAE,KAAK;AAAE,cAAGC,GAAE;AAAK,mBAAM;AAAG,gBAAK,CAACK,IAAEC,EAAC,IAAEN,GAAE;AAAM,cAAGA,KAAEM,IAAEF,OAAIC,MAAGF,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC;AAAE,mBAAM;AAAA,QAAE;AAAC,eAAM,CAAC,CAACD,GAAE,KAAK,EAAE;AAAA,MAAI,EAAEK,GAAE,GAAEH,EAAC;AAAE,QAAAC,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWE,GAAE,CAAC;AAAA,WAAM;AAAC,QAAAA,GAAE,IAAEH;AAAE,cAAMF,KAAE,MAAM,IAAI,EAAE,KAAK,CAAC;AAAE,QAAAE,GAAE,QAAS,CAACG,IAAEJ,OAAI;AAAC,cAAG,MAAII,GAAE;AAAO,kBAAM,MAAM,kBAAkBJ,EAAC,+BAA+B;AAAE,UAAAD,GAAE,IAAEC,EAAC,IAAEI,GAAE,CAAC,GAAEL,GAAE,IAAEC,KAAE,CAAC,IAAEI,GAAE,CAAC,GAAEL,GAAE,IAAEC,KAAE,CAAC,IAAEI,GAAE,CAAC,GAAEL,GAAE,IAAEC,KAAE,CAAC,IAAEI,GAAE,CAAC;AAAA,QAAC,CAAE,GAAEF,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWE,GAAE,CAAC,GAAEF,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAK,KAAI,GAAE,GAAEA,GAAE,MAAKA,GAAE,eAAc,IAAI,WAAWH,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,EAAEG,IAAEH,IAAEM,IAAEJ,EAAC,GAAEE,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC;AAAE,UAAMC,KAAEF,GAAE;AAAE,IAAAE,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGE,EAAC,GAAED,KAAE,SAASC,IAAE;AAAC,WAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAA,EAAC,EAAEA,EAAC,GAAEC,KAAE,MAAM,QAAQL,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA,IAAEM,KAAE,MAAM,QAAQL,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA;AAAE,KAAGE,IAAED,IAAE,MAAI,MAAI;AAAC,QAAIE,KAAED,GAAE;AAAE,IAAAC,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWL,EAAC,GAAEK,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWD,GAAE,CAAC,GAAEC,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcC,EAAC,GAAED,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWD,GAAE,CAAC,GAAEC,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcE,EAAC,GAAEJ,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,IAAGE,KAAED,GAAE,GAAG,cAAcC,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,IAAAK,cAAa,4BAA0BA,cAAa,qCAAmC,KAAK,IAAEA,IAAE,KAAK,IAAEL,MAAG,KAAK,IAAEK;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAE;AAAC,QAAGK,IAAE;AAAC,UAAIJ,KAAE,GAAG,IAAI;AAAE,MAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK;AAAE,UAAIC,KAAED,GAAE,QAAOE,KAAE;AAAE,iBAAUC,MAAKC;AAAE,QAAAJ,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAEH,GAAE,cAAY,GAAGD,GAAE,OAAM,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAEH,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,IAAGC,KAAE,IAAI,UAAQ,IAAID,GAAE,IAAEF,GAAE,OAAME,GAAE,IAAEF,GAAE,QAAO,GAAGF,GAAE,QAAO,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAE,GAAE,IAAE,KAAK,EAAE,GAAEH,GAAE,KAAKI,EAAC,GAAEJ,GAAE,OAAOI,EAAC,GAAE,EAAEF;AAAE,MAAAF,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGI,IAAEL,IAAEC,IAAE;AAAC,QAAGI,MAAGL,IAAE;AAAC,UAAIE,KAAE,GAAG,IAAI;AAAE,MAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK;AAAE,UAAIC,KAAED,GAAE,QAAOE,KAAE;AAAE,iBAAUE,MAAKN,IAAE;AAAC,QAAAE,GAAE,UAAU,GAAEF,KAAEK,GAAEC,GAAE,KAAK;AAAE,cAAMC,KAAEF,GAAEC,GAAE,GAAG;AAAE,QAAAN,MAAGO,OAAIL,GAAE,cAAY,GAAGD,GAAE,OAAM,EAAC,OAAMG,IAAE,MAAKJ,IAAE,IAAGO,GAAC,CAAC,GAAEL,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKJ,IAAE,IAAGO,GAAC,CAAC,GAAEL,GAAE,OAAOF,GAAE,IAAEG,GAAE,OAAMH,GAAE,IAAEG,GAAE,MAAM,GAAED,GAAE,OAAOK,GAAE,IAAEJ,GAAE,OAAMI,GAAE,IAAEJ,GAAE,MAAM,IAAG,EAAEC,IAAEF,GAAE,OAAO;AAAA,MAAC;AAAC,MAAAA,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGG,IAAEL,IAAE;AAAC,UAAMC,KAAE,GAAG,IAAI;AAAE,IAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK,GAAEA,GAAE,UAAU,GAAEA,GAAE,YAAU,GAAGD,GAAE,WAAU,CAAC,CAAC,GAAEC,GAAE,cAAY,GAAGD,GAAE,OAAM,CAAC,CAAC,GAAEC,GAAE,YAAU,GAAGD,GAAE,WAAU,CAAC,CAAC,GAAEC,GAAE,OAAOI,GAAE,SAAQA,GAAE,OAAO,GAAEJ,GAAE,OAAOI,GAAE,UAAQA,GAAE,OAAMA,GAAE,OAAO,GAAEJ,GAAE,OAAOI,GAAE,UAAQA,GAAE,OAAMA,GAAE,UAAQA,GAAE,MAAM,GAAEJ,GAAE,OAAOI,GAAE,SAAQA,GAAE,UAAQA,GAAE,MAAM,GAAEJ,GAAE,OAAOI,GAAE,SAAQA,GAAE,OAAO,GAAEJ,GAAE,OAAO,GAAEA,GAAE,KAAK,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,GAAGI,IAAEL,IAAEC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAG,GAAE;AAAC,SAAK,IAAE,SAASI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,GAAGE,EAAC;AAAE,SAAGA,IAAEL,IAAG,CAAAA,OAAG;AAAC,WAAGK,IAAEL,IAAEC,IAAEC,EAAC,IAAGF,KAAE,GAAGK,EAAC,GAAG,UAAUF,GAAE,QAAO,GAAE,GAAEH,GAAE,OAAO,OAAMA,GAAE,OAAO,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,MAAKK,IAAEJ,IAAED,EAAC,IAAE,GAAG,MAAKK,GAAE,EAAE,GAAEJ,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGK,IAAEL,IAAEC,IAAE;AAAC,SAAK,IAAE,SAASI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,GAAGE,EAAC;AAAE,SAAGA,IAAEL,IAAG,CAAAA,OAAG;AAAC,WAAGK,IAAEL,IAAEC,IAAEC,EAAC,IAAGF,KAAE,GAAGK,EAAC,GAAG,UAAUF,GAAE,QAAO,GAAE,GAAEH,GAAE,OAAO,OAAMA,GAAE,OAAO,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,MAAKK,IAAEL,IAAEC,EAAC,IAAE,GAAG,MAAKI,GAAE,EAAE,GAAEL,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAtq+D,QAAAS,KAAA;AAAuq+D,KAAAA,MAAA,KAAK,MAAL,gBAAAA,IAAQ,SAAQ,KAAK,IAAE,SAAO,UAAK,MAAL,mBAAQ,SAAQ,KAAK,IAAE,SAAO,UAAK,MAAL,mBAAQ,SAAQ,KAAK,IAAE;AAAA,EAAM;AAAC;AAAE,SAAS,GAAGL,IAAEL,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAE,aAAOK,GAAE,EAAE,KAAM,CAAAA,OAAGA,cAAa,SAAU;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAG,eAAa,OAAO,eAAaA,cAAa,WAAY;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,KAAM,CAAAA,OAAG,eAAa,OAAO,gBAAcA,cAAa,YAAa;AAAA,IAAE;AAAQ,YAAM,MAAM,0BAA0BL,EAAC,EAAE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAE,GAAGK,IAAE,CAAC;AAAE,MAAG,CAACL,IAAE;AAAC,IAAAA,KAAE,GAAGK,EAAC;AAAE,UAAMJ,KAAE,GAAGI,EAAC,GAAEH,KAAE,IAAI,WAAWG,GAAE,QAAMA,GAAE,SAAO,CAAC;AAAE,OAAGJ,IAAED,IAAE,GAAGK,EAAC,CAAC,GAAEL,GAAE,WAAW,GAAE,GAAEK,GAAE,OAAMA,GAAE,QAAOL,GAAE,MAAKA,GAAE,eAAcE,EAAC,GAAE,GAAGD,EAAC,GAAED,KAAE,IAAI,UAAU,IAAI,kBAAkBE,GAAE,MAAM,GAAEG,GAAE,OAAMA,GAAE,MAAM,GAAEA,GAAE,EAAE,KAAKL,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAIL,KAAE,GAAGK,IAAE,CAAC;AAAE,MAAG,CAACL,IAAE;AAAC,UAAMC,KAAE,GAAGI,EAAC;AAAE,IAAAL,KAAE,GAAGK,EAAC;AAAE,UAAMH,KAAE,GAAGG,IAAE,CAAC,KAAG,GAAGA,EAAC;AAAE,IAAAJ,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcC,EAAC,GAAE,GAAGG,EAAC;AAAA,EAAC;AAAC,SAAOL;AAAC;AAAC,SAAS,GAAGK,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,UAAM,MAAM,qGAAqG;AAAE,SAAOA,GAAE,MAAIA,GAAE,IAAE,GAAGA,GAAE,OAAO,WAAW,QAAQ,GAAE,yFAAyF,IAAGA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAML,KAAE,GAAGK,EAAC;AAAE,EAAAL,GAAE,SAAS,GAAE,GAAEK,GAAE,OAAMA,GAAE,MAAM,GAAEL,GAAE,cAAcA,GAAE,QAAQ;AAAE,MAAIC,KAAE,GAAGI,IAAE,CAAC;AAAE,SAAOJ,OAAIA,KAAE,GAAG,GAAGI,EAAC,GAAEL,EAAC,GAAEK,GAAE,EAAE,KAAKJ,EAAC,GAAEI,GAAE,IAAE,OAAIL,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGI,IAAE;AAAC,EAAAA,GAAE,EAAE,YAAYA,GAAE,EAAE,YAAW,IAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAML,KAAE,GAAGK,EAAC;AAAE,SAAO,GAAG,GAAGA,EAAC,GAAEL,IAAE,MAAI,MAAI,SAASK,IAAEL,IAAE;AAAC,UAAMC,KAAEI,GAAE;AAAO,QAAGJ,GAAE,UAAQI,GAAE,SAAOJ,GAAE,WAASI,GAAE;AAAO,aAAOL,GAAE;AAAE,UAAME,KAAED,GAAE,OAAME,KAAEF,GAAE;AAAO,WAAOA,GAAE,QAAMI,GAAE,OAAMJ,GAAE,SAAOI,GAAE,QAAOA,KAAEL,GAAE,GAAEC,GAAE,QAAMC,IAAED,GAAE,SAAOE,IAAEE;AAAA,EAAC,EAAEA,IAAG,MAAI;AAAC,QAAGL,GAAE,gBAAgBA,GAAE,aAAY,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,EAAEK,GAAE,kBAAkB;AAAiB,YAAM,MAAM,oGAAoG;AAAE,WAAOA,GAAE,OAAO,sBAAsB;AAAA,EAAC,CAAE,CAAE;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,qBAAmB,GAAG,UAAU,IAAG,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,gBAAc,GAAG,UAAU,IAAG,GAAG,OAAK,SAASA,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGD,MAAG,KAAGG,KAAEL,OAAIC,KAAED,OAAIG,MAAG,KAAGF,KAAEI,OAAIJ,KAAED,MAAIE,IAAEC,EAAC;AAAC,GAAE,GAAG,QAAM;AAAG,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYE,IAAEL,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,SAAK,IAAED,IAAE,KAAK,IAAEL,IAAE,KAAK,IAAEC,IAAE,KAAK,SAAOC,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOE,KAAG,KAAK,KAAG,KAAK,OAAK,MAAI,EAAE,MAAI,QAAQ,MAAM,4FAA4F;AAAA,EAAE;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAID,KAAE,GAAG,MAAK,CAAC;AAAE,WAAOA,OAAI,GAAG,IAAI,GAAE,GAAG,IAAI,GAAEA,KAAE,GAAG,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,EAAE,KAAKA,EAAC,GAAE,KAAK,IAAE,OAAIA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,CAAC;AAAE,eAAUL,MAAK,KAAK,GAAE;AAAC,UAAIC;AAAE,UAAGD,cAAa;AAAU,QAAAC,KAAE,IAAI,UAAUD,GAAE,MAAK,KAAK,OAAM,KAAK,MAAM;AAAA,eAAUA,cAAa,cAAa;AAAC,cAAMK,KAAE,GAAG,IAAI,GAAEL,KAAE,GAAG,IAAI;AAAE,QAAAK,GAAE,cAAcA,GAAE,QAAQ,GAAEJ,KAAE,GAAGD,IAAEK,EAAC,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,EAAC,GAAEI,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAK,KAAK,OAAM,KAAK,QAAO,GAAEA,GAAE,MAAKA,GAAE,eAAc,IAAI,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAE,GAAGL,IAAEK,IAAEJ,EAAC,GAAE,GAAGD,IAAEK,IAAE,OAAI,MAAI;AAAC,aAAG,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,GAAG,IAAI;AAAA,QAAC,CAAE,GAAE,GAAGL,EAAC,GAAE,GAAG,IAAI;AAAA,MAAC,OAAK;AAAC,YAAG,EAAEA,cAAa;AAAa,gBAAM,MAAM,0BAA0BA,EAAC,EAAE;AAAE,WAAG,IAAI,GAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI,GAAE,GAAG,IAAI;AAAA,MAAC;AAAC,MAAAI,GAAE,KAAKJ,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,GAAGI,IAAE,KAAK,GAAG,GAAE,KAAK,EAAE,GAAE,KAAK,QAAO,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,GAAG,MAAK,CAAC,EAAE,MAAM,GAAE,KAAK,KAAG,GAAG,IAAI,EAAE,cAAc,GAAG,MAAK,CAAC,CAAC,GAAE,KAAG;AAAA,EAAE;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,oBAAkB,GAAG,UAAU,GAAE,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,GAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,eAAa,GAAG,UAAU;AAAG,IAAI,KAAG;AAAI,SAAS,MAAMA,IAAE;AAAC,SAAOA,GAAE,IAAK,CAAC,CAACA,IAAEL,EAAC,OAAK,EAAC,OAAMK,IAAE,KAAIL,GAAC,EAAG;AAAC;AAAC,IAAM,KAAG,SAASK,IAAE;AAAC,SAAO,cAAcA,GAAC;AAAA,IAAC,KAAI;AAAC,WAAK,EAAE,oCAAoC;AAAA,IAAC;AAAA,EAAC;AAAC,GAAG,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,SAAK,IAAE,MAAG,KAAK,IAAEK,IAAE,KAAK,IAAE,MAAK,KAAK,IAAE,GAAE,KAAK,IAAE,cAAY,OAAO,KAAK,EAAE,sBAAqB,WAASL,KAAE,KAAK,EAAE,SAAOA,KAAE,GAAG,IAAE,KAAK,EAAE,SAAO,IAAI,gBAAgB,GAAE,CAAC,KAAG,QAAQ,KAAK,oHAAoH,GAAE,KAAK,EAAE,SAAO,SAAS,cAAc,QAAQ;AAAA,EAAE;AAAA,EAAC,MAAM,gBAAgBK,IAAE;AAAC,UAAML,KAAE,OAAM,MAAM,MAAMK,EAAC,GAAG,YAAY;AAAE,IAAAA,KAAE,EAAEA,GAAE,SAAS,QAAQ,KAAGA,GAAE,SAAS,YAAY,IAAG,KAAK,SAAS,IAAI,WAAWL,EAAC,GAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,SAAU,IAAI,cAAa,OAAOA,EAAC,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEL,IAAE;AAAC,UAAMC,KAAEI,GAAE,QAAOH,KAAE,KAAK,EAAE,QAAQD,EAAC;AAAE,SAAK,EAAE,OAAO,IAAII,IAAEH,EAAC,GAAEF,KAAE,KAAK,EAAE,mBAAmBC,IAAEC,EAAC,IAAE,KAAK,EAAE,iBAAiBD,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeG,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,EAAE,mBAAiB,QAAQ,KAAK,kHAAkH,GAAE,GAAG,MAAKD,MAAG,eAAe,CAAAA,OAAG;AAAC,SAAG,MAAKC,KAAEA,MAAG,gBAAgB,CAAAA,OAAG;AAAC,aAAK,EAAE,gBAAgBD,IAAEC,IAAEE,IAAEL,IAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBI,IAAE;AAAC,SAAK,IAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,SAAK,EAAE,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAAC,SAAK,EAAE,sCAAoCA;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAE;AAAC,OAAG,MAAK,oBAAoB,CAAAL,OAAG;AAAC,MAAAK,GAAEL,EAAC;AAAA,IAAC,CAAE,GAAE,GAAG,MAAK,oBAAoB,CAAAK,OAAG;AAAC,WAAK,EAAE,gBAAgBA,IAAE,MAAM;AAAA,IAAC,CAAE,GAAE,OAAO,KAAK,EAAE,gBAAgB;AAAA,EAAgB;AAAA,EAAC,oBAAoBA,IAAE;AAAC,SAAK,EAAE,gBAAcA;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEL,IAAE;AAAC,SAAK,EAAE,uBAAqB,KAAK,EAAE,wBAAsB,CAAC,GAAE,KAAK,EAAE,qBAAqBK,EAAC,IAAEL;AAAA,EAAC;AAAA,EAAC,iBAAiBK,IAAEL,IAAEC,IAAE;AAAC,SAAK,0BAA0BI,IAAE,GAAE,GAAEL,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BI,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAEC,GAAE;AAAO,SAAK,MAAID,OAAI,KAAK,KAAG,KAAK,EAAE,MAAM,KAAK,CAAC,GAAE,KAAK,IAAE,KAAK,EAAE,QAAQA,EAAC,GAAE,KAAK,IAAEA,KAAG,KAAK,EAAE,QAAQ,IAAIC,IAAE,KAAK,IAAE,CAAC,GAAE,GAAG,MAAKH,IAAG,CAAAG,OAAG;AAAC,WAAK,EAAE,uBAAuB,KAAK,GAAEL,IAAEC,IAAEI,IAAEF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBE,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAK,CAACE,IAAEC,EAAC,IAAE,GAAG,MAAKE,IAAEL,EAAC;AAAE,WAAK,EAAE,yBAAyBA,IAAEE,IAAEC,IAAEF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBK,IAAEL,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,wBAAwBK,IAAEL,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,uBAAuBK,IAAEL,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBK,IAAEL,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBK,IAAEL,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,aAAK,EAAE,wBAAwBA,IAAEL,IAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAK,OAAO,KAAKK,EAAC,GAAG,CAAAH,OAAG;AAAC,WAAG,MAAK,OAAO,OAAOG,EAAC,GAAG,CAAAF,OAAG;AAAC,eAAK,EAAE,6BAA6BD,IAAEC,IAAE,OAAO,KAAKE,EAAC,EAAE,QAAOL,IAAEC,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,cAAMG,KAAE,KAAK,EAAE,QAAQE,GAAE,MAAM;AAAE,aAAK,EAAE,OAAO,IAAIA,IAAEF,EAAC,GAAE,KAAK,EAAE,uBAAuBA,IAAEE,GAAE,QAAOL,IAAEC,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBE,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,6BAA6BA,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBK,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,oBAAoBG,GAAE,MAAM;AAAE,UAAG,CAACH;AAAE,cAAM,MAAM,6CAA6C;AAAE,iBAAUF,MAAKK;AAAE,aAAK,EAAE,oBAAoBH,IAAEF,EAAC;AAAE,WAAK,EAAE,4BAA4BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,sBAAsBG,GAAE,MAAM;AAAE,UAAG,CAACH;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUF,MAAKK;AAAE,aAAK,EAAE,sBAAsBH,IAAEF,EAAC;AAAE,WAAK,EAAE,8BAA8BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,qBAAqBG,GAAE,MAAM;AAAE,UAAG,CAACH;AAAE,cAAM,MAAM,8CAA8C;AAAE,iBAAUF,MAAKK;AAAE,aAAK,EAAE,qBAAqBH,IAAEF,EAAC;AAAE,WAAK,EAAE,6BAA6BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,mBAAmBG,GAAE,MAAM;AAAE,UAAG,CAACH;AAAE,cAAM,MAAM,4CAA4C;AAAE,iBAAUF,MAAKK;AAAE,aAAK,EAAE,mBAAmBH,IAAEF,EAAC;AAAE,WAAK,EAAE,2BAA2BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,oBAAoBG,GAAE,MAAM;AAAE,UAAG,CAACH;AAAE,cAAM,MAAM,qDAAqD;AAAE,iBAAUF,MAAKK;AAAE,aAAK,EAAE,oBAAoBH,IAAEF,EAAC;AAAE,WAAK,EAAE,4BAA4BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,sBAAsBG,GAAE,MAAM;AAAE,UAAG,CAACH;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUF,MAAKK;AAAE,WAAG,MAAKL,IAAG,CAAAK,OAAG;AAAC,eAAK,EAAE,sBAAsBH,IAAEG,EAAC;AAAA,QAAC,CAAE;AAAE,WAAK,EAAE,8BAA8BH,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBI,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BK,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BK,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BK,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,yBAAyBK,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BK,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,aAAK,EAAE,4BAA4BA,IAAEL,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BK,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,cAAME,KAAE,KAAK,EAAE,QAAQG,GAAE,MAAM;AAAE,aAAK,EAAE,OAAO,IAAIA,IAAEH,EAAC,GAAE,KAAK,EAAE,2BAA2BA,IAAEG,GAAE,QAAOL,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BG,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,oBAAoBI,GAAE,MAAM;AAAE,UAAG,CAACJ;AAAE,cAAM,MAAM,6CAA6C;AAAE,iBAAUD,MAAKK;AAAE,aAAK,EAAE,oBAAoBJ,IAAED,EAAC;AAAE,WAAK,EAAE,gCAAgCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iCAAiCK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,sBAAsBI,GAAE,MAAM;AAAE,UAAG,CAACJ;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUD,MAAKK;AAAE,aAAK,EAAE,sBAAsBJ,IAAED,EAAC;AAAE,WAAK,EAAE,kCAAkCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gCAAgCK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,qBAAqBI,GAAE,MAAM;AAAE,UAAG,CAACJ;AAAE,cAAM,MAAM,8CAA8C;AAAE,iBAAUD,MAAKK;AAAE,aAAK,EAAE,qBAAqBJ,IAAED,EAAC;AAAE,WAAK,EAAE,iCAAiCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,8BAA8BK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,mBAAmBI,GAAE,MAAM;AAAE,UAAG,CAACJ;AAAE,cAAM,MAAM,4CAA4C;AAAE,iBAAUD,MAAKK;AAAE,aAAK,EAAE,mBAAmBJ,IAAED,EAAC;AAAE,WAAK,EAAE,+BAA+BC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,oBAAoBI,GAAE,MAAM;AAAE,UAAG,CAACJ;AAAE,cAAM,MAAM,qDAAqD;AAAE,iBAAUD,MAAKK;AAAE,aAAK,EAAE,oBAAoBJ,IAAED,EAAC;AAAE,WAAK,EAAE,gCAAgCC,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iCAAiCK,IAAEL,IAAE;AAAC,OAAG,MAAKA,IAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,sBAAsBI,GAAE,MAAM;AAAE,UAAG,CAACJ;AAAE,cAAM,MAAM,+CAA+C;AAAE,iBAAUD,MAAKK;AAAE,WAAG,MAAKL,IAAG,CAAAK,OAAG;AAAC,eAAK,EAAE,sBAAsBJ,IAAEI,EAAC;AAAA,QAAC,CAAE;AAAE,WAAK,EAAE,kCAAkCJ,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBK,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,oBAAoBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,mBAAmBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,yBAAyBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,oBAAoBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKI,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,IAAEJ,MAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKI,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BA,IAAEJ,MAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBI,IAAEL,IAAEC,IAAE;AAAC,SAAK,EAAE,wBAAsB,QAAQ,KAAK,4HAA4H,GAAE,GAAG,MAAKI,IAAG,CAACA,IAAEJ,OAAI;AAAC,MAAAI,KAAE,IAAI,aAAaA,GAAE,QAAOA,GAAE,YAAWA,GAAE,SAAO,CAAC,GAAEL,GAAEK,IAAEJ,EAAC;AAAA,IAAC,CAAE,GAAE,GAAG,MAAKI,IAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,IAAEJ,MAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,EAAE,eAAe;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,EAAE,YAAY,GAAE,KAAK,EAAE,kBAAgB,QAAO,KAAK,EAAE,uBAAqB;AAAA,EAAM;AAAC,GAAE,cAAc,GAAE;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,GAAGI,IAAEL,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAG,CAAAA,OAAG;AAAC,YAAK,CAACE,IAAEC,EAAC,IAAE,GAAG,MAAKE,IAAEL,EAAC;AAAE,WAAK,GAAG,gCAAgCA,IAAEE,IAAEC,IAAEF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,EAAEI,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,GAAG,qBAAqBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAE;AAAC,OAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,MAAKK,IAAG,CAAAA,OAAG;AAAC,WAAK,GAAG,2BAA2BA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,EAAE;AAAE,IAAI;AAAJ,IAAO,KAAG,cAAc,GAAE;AAAC;AAAE,eAAe,GAAGA,IAAEL,IAAEC,IAAE;AAAC,SAAO,eAAeI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAGG,IAAEL,IAAEC,IAAEC,EAAC;AAAA,EAAC,EAAEG,IAAEJ,GAAE,WAAS,GAAG,IAAE,SAAO,SAAS,cAAc,QAAQ,IAAGD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGI,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAGG,GAAE,GAAE;AAAC,UAAMD,KAAE,IAAI;AAAG,QAAGH,MAAA,gBAAAA,GAAG,kBAAiB;AAAC,UAAG,CAACI,GAAE;AAAG,cAAM,MAAM,+CAA+C;AAAE,UAAIF,KAAEF,GAAE;AAAiB,UAAGE,GAAE,QAAMA,GAAE,SAAOA,GAAE,OAAKA,GAAE;AAAO,cAAM,MAAM,oDAAoD;AAAE,UAAGA,GAAE,OAAK,KAAGA,GAAE,MAAI,KAAGA,GAAE,QAAM,KAAGA,GAAE,SAAO;AAAE,cAAM,MAAM,uCAAuC;AAAE,SAAGC,IAAE,IAAGD,GAAE,OAAKA,GAAE,SAAO,CAAC,GAAE,GAAGC,IAAE,IAAGD,GAAE,MAAIA,GAAE,UAAQ,CAAC,GAAE,GAAGC,IAAE,GAAED,GAAE,QAAMA,GAAE,IAAI,GAAE,GAAGC,IAAE,GAAED,GAAE,SAAOA,GAAE,GAAG;AAAA,IAAC;AAAM,SAAGC,IAAE,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAE,CAAC,GAAE,GAAGA,IAAE,GAAE,CAAC;AAAE,QAAGH,MAAA,gBAAAA,GAAG,iBAAgB;AAAC,WAAGA,MAAA,gBAAAA,GAAG,mBAAgB,MAAI;AAAE,cAAM,MAAM,4CAA4C;AAAE,UAAG,GAAGG,IAAE,GAAE,CAAC,KAAK,KAAGH,GAAE,kBAAgB,GAAG,IAAEA,MAAA,gBAAAA,GAAG,mBAAgB,OAAK,GAAE;AAAC,cAAK,CAACI,IAAEH,EAAC,IAAE,GAAGF,EAAC;AAAE,QAAAC,KAAE,GAAGG,IAAE,CAAC,IAAEF,KAAEG,IAAEF,KAAE,GAAGC,IAAE,CAAC,IAAEC,KAAEH,IAAE,GAAGE,IAAE,GAAEH,EAAC,GAAE,GAAGG,IAAE,GAAED,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAE,GAAE,EAAE,iBAAiBD,GAAE,EAAE,GAAE,4BAA2BC,GAAE,GAAEH,EAAC;AAAA,EAAC;AAAC,EAAAG,GAAE,EAAE,GAAGL,IAAEK,GAAE,IAAGH,MAAG,YAAY,IAAI,CAAC,GAAEG,GAAE,iBAAiB;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAE;AAAty5E,MAAAS;AAAuy5E,OAAGA,MAAAL,GAAE,gBAAF,gBAAAK,IAAe;AAAI,UAAM,MAAM,gFAAgF;AAAE,KAAGL,IAAEL,IAAEC,IAAEI,GAAE,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAA/75E,MAAAQ;AAAg85E,MAAG,GAACA,MAAAL,GAAE,gBAAF,gBAAAK,IAAe;AAAI,UAAM,MAAM,gFAAgF;AAAE,KAAGL,IAAEL,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGG,IAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAE;AAAK,QAAMI,KAAEJ,GAAE,OAAMM,KAAEF,MAAGJ,KAAEA,GAAE;AAAQ,OAAIG,cAAa,cAAYA,cAAa,iBAAeA,GAAE,WAASG;AAAE,UAAM,MAAM,gCAA8BH,GAAE,SAAOG,EAAC;AAAE,SAAOD,KAAE,IAAI,GAAG,CAACF,EAAC,GAAEF,IAAE,OAAGI,GAAE,EAAE,EAAE,QAAOA,GAAE,GAAED,IAAEJ,EAAC,GAAEE,KAAEG,GAAE,MAAM,IAAEA;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMG,EAAC,GAAE,KAAK,IAAEA,IAAE,KAAK,KAAGL,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAGC,IAAE,KAAK,IAAE,IAAI;AAAA,EAAE;AAAA,EAAC,EAAEG,IAAEL,KAAE,MAAG;AAAC,QAAG,iBAAgBK,MAAG,GAAG,KAAK,aAAY,GAAE,CAAC,CAACA,GAAE,eAAa,YAAUA,GAAE,WAAW,GAAE,WAASA,GAAE,UAAQ,KAAK,EAAE,EAAE,WAASA,GAAE;AAAO,YAAM,MAAM,iDAAiD;AAAE,WAAO,MAAM,EAAEA,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,MAAM,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,gBAAe,KAAE,GAAE,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,4BAA2BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,0BAAwB,GAAE,GAAE,6BAA4BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,2BAAyB,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKK,IAAEL,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAEK,IAAEL,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,cAAc,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wDAAwD,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,wBAAwB,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACI,IAAEL,OAAI;AAAC,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,EAAE,WAAW,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,eAAeA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,GAAG,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAArX,IAAuX,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAA5hB,IAA8hB,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAnnB,IAAqnB,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAlqB,IAAoqB,KAAG,GAAG,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAn0B,IAAq0B,KAAG,GAAG,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,CAAC;AAA74B,IAA+4B,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAA57B,IAA87B,KAAG,GAAG,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,CAAC;AAA/xC,IAAiyC,KAAG,CAAC,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,EAAE;AAAx0C,IAA00C,KAAG,GAAG,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAE,SAAS,GAAGK,IAAE;AAAC,EAAAA,GAAE,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,8BAA6B,CAAC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,8BAA6B,CAAC,EAAC,GAAE,KAAK,qCAAmC,KAAK,wBAAsB,OAAG,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,2BAA0BA,OAAI,KAAK,wBAAsB,CAAC,CAACA,GAAE,wBAAuB,wCAAuCA,OAAI,KAAK,qCAAmC,CAAC,CAACA,GAAE,qCAAoC,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKK,IAAEL,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAEK,IAAEL,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,+BAA+B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACI,IAAEL,OAAI;AAAC,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,EAAE,cAAc,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,0BAAwB,GAAGA,IAAE,aAAa,GAAE,GAAGJ,IAAE,yBAAyB,GAAE,KAAK,EAAE,0BAA0B,eAAe,CAACI,IAAEL,OAAI;AAAC,UAAG,KAAK;AAAsB,mBAAUA,MAAKK;AAAE,UAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,EAAE,gBAAgB,KAAK,GAAGK,GAAE,EAAE,KAAG,CAAC,CAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,eAAe,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,uCAAqC,GAAGA,IAAE,eAAe,GAAE,GAAGJ,IAAE,6BAA6B,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAACI,IAAEL,OAAI;AAAC,UAAG,KAAK;AAAmC,mBAAUA,MAAKK;AAAE,WAACA,KAAE,GAAG,GAAGL,EAAC,GAAE,IAAG,CAAC,MAAI,KAAK,EAAE,6BAA6B,KAAK,EAAC,MAAK,GAAG,GAAGK,IAAE,CAAC,GAAE,CAAC,KAAG,GAAE,SAAQ,GAAG,GAAGA,IAAE,CAAC,GAAE,CAAC,KAAG,GAAE,MAAK,GAAGA,IAAE,GAAE,IAAG,GAAG,CAAC,EAAE,MAAM,KAAG,CAAC,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAGA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC,GAAE,GAAG,sBAAoB,IAAG,GAAG,0BAAwB,IAAG,GAAG,8BAA4B,IAAG,GAAG,2BAAyB,IAAG,GAAG,2BAAyB,IAAG,GAAG,+BAA6B,IAAG,GAAG,4BAA0B,IAAG,GAAG,2BAAyB,IAAG,GAAG,0BAAwB,IAAG,GAAG,6BAA2B;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,IAAE,GAAE,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,QAAG,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,MAAKI,IAAEH,MAAG,CAAC,CAAC,GAAE,CAAC,KAAK;AAAE,aAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIG,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wDAAwD,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,+BAA+B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,EAAE,kBAAkB,CAACI,IAAEL,OAAI;AAAC,UAAIC,KAAE,CAAC,KAAK,GAAEC,KAAEG,GAAE,MAAKF,KAAEE,GAAE;AAAM,YAAMD,KAAED,MAAGE,KAAEA,GAAE;AAAQ,UAAGH,cAAa;AAAW,YAAGA,GAAE,WAAS,IAAEE,IAAE;AAAC,gBAAMJ,KAAE,IAAI,kBAAkB,IAAEI,EAAC;AAAE,mBAAQC,KAAE,GAAEA,KAAED,IAAE,EAAEC;AAAE,YAAAL,GAAE,IAAEK,EAAC,IAAEH,GAAE,IAAEG,EAAC,GAAEL,GAAE,IAAEK,KAAE,CAAC,IAAEH,GAAE,IAAEG,KAAE,CAAC,GAAEL,GAAE,IAAEK,KAAE,CAAC,IAAEH,GAAE,IAAEG,KAAE,CAAC,GAAEL,GAAE,IAAEK,KAAE,CAAC,IAAE;AAAI,UAAAH,KAAE,IAAI,UAAUF,IAAEG,IAAEE,EAAC;AAAA,QAAC,OAAK;AAAC,cAAGH,GAAE,WAAS,IAAEE;AAAE,kBAAM,MAAM,gCAA8BF,GAAE,SAAOE,EAAC;AAAE,UAAAF,KAAE,IAAI,UAAU,IAAI,kBAAkBA,GAAE,QAAOA,GAAE,YAAWA,GAAE,MAAM,GAAEC,IAAEE,EAAC;AAAA,QAAC;AAAA,eAAS,EAAEH,cAAa;AAAc,cAAM,MAAM,uBAAuBA,GAAE,YAAY,IAAI,EAAE;AAAE,MAAAC,KAAE,IAAI,GAAG,CAACD,EAAC,GAAE,OAAG,OAAG,KAAK,EAAE,EAAE,QAAO,KAAK,GAAEC,IAAEE,EAAC,GAAE,KAAK,IAAEJ,KAAEA,KAAEE,GAAE,MAAM,IAAEA,IAAE,KAAK,KAAG,KAAK,EAAEF,EAAC,GAAE,GAAG,MAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,WAAK,IAAE,MAAK,KAAK,KAAG,KAAK,EAAE,IAAI,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,UAAQ,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,GAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,GAAGK,IAAE;AAAC,EAAAA,GAAE,WAAS,CAAC,GAAEA,GAAE,YAAU,CAAC,GAAEA,GAAE,iBAAe,CAAC,GAAEA,GAAE,aAAW,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,MAAIA,GAAE,SAAS,SAAO,EAAC,UAAS,CAAC,GAAE,WAAU,CAAC,GAAE,gBAAe,CAAC,GAAE,YAAW,CAAC,GAAE,cAAa,CAAC,EAAC,IAAE,EAAC,UAASA,GAAE,UAAS,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,YAAWA,GAAE,YAAW,cAAaA,GAAE,WAAU;AAAC;AAAC,SAAS,GAAGA,IAAEL,KAAE,MAAG;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUE,MAAKE,IAAE;AAAC,QAAIH,KAAE,GAAGC,EAAC;AAAE,IAAAE,KAAE,CAAC;AAAE,eAAUJ,MAAKC,GAAE,EAAE;AAAE,MAAAA,KAAEF,MAAG,QAAM,GAAGC,IAAE,CAAC,IAAE,GAAG,GAAGA,IAAE,CAAC,GAAE,CAAC,IAAE,IAAGI,GAAE,KAAK,EAAC,OAAM,GAAGJ,IAAE,CAAC,KAAG,GAAE,OAAMC,IAAE,cAAa,GAAGD,IAAE,CAAC,KAAG,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,GAAE,CAAC;AAAE,IAAAA,GAAE,KAAKI,EAAC;AAAA,EAAC;AAAC,SAAOJ;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYI,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAjj8G,QAAAK,KAAA;AAAkj8G,QAAG,GAAG,KAAK,GAAE,GAAEL,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAEA,GAAE,iCAAgC;AAAC,UAAIL,KAAE,IAAI,MAAGC,KAAED,IAAEE,KAAE,GAAGG,GAAE,kCAAgCK,MAAA,GAAG,KAAK,GAAE,IAAG,CAAC,MAAd,gBAAAA,IAAiB,GAAG;AAAE,SAAGT,IAAE,GAAE,GAAEC,EAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAEF,EAAC;AAAA,IAAC;AAAM,iBAASK,GAAE,qCAAiC,QAAG,KAAK,GAAE,IAAG,CAAC,MAAd,mBAAiB;AAAI,WAAOA,GAAE,mCAAiC,GAAGJ,KAAED,KAAE,IAAI,MAAG,GAAE,GAAEE,KAAE,GAAGG,GAAE,kCAAgC,QAAG,KAAK,GAAE,IAAG,CAAC,MAAd,mBAAiB,GAAG,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAEL,EAAC,KAAG,WAASK,GAAE,qCAAiC,QAAG,KAAK,GAAE,IAAG,CAAC,MAAd,mBAAiB,MAAI,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGK,IAAEL,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,eAAe,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,kEAAkE,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,6BAA6B,GAAE,GAAGA,IAAE,0BAA0B,GAAE,GAAGA,IAAE,sCAAsC,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACI,IAAEL,OAAI;AAAC,iBAAUA,MAAKK,IAAE;AAAC,QAAAA,KAAE,GAAGL,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAK,GAAGK,IAAE,IAAG,CAAC;AAAE,UAAAJ,GAAE,KAAK,EAAC,GAAE,GAAGD,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,aAAK,UAAU,KAAKC,EAAC;AAAA,MAAC;AAAC,SAAG,MAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAACA,IAAEL,OAAI;AAAC,iBAAUA,MAAKK,IAAE;AAAC,QAAAA,KAAE,GAAGL,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAK,GAAGK,IAAE,IAAG,CAAC;AAAE,UAAAJ,GAAE,KAAK,EAAC,GAAE,GAAGD,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,aAAK,eAAe,KAAKC,EAAC;AAAA,MAAC;AAAC,SAAG,MAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAACA,IAAEL,OAAI;AAAC,WAAK,SAAS,KAAK,GAAG,GAAGK,IAAE,KAAE,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACA,IAAEL,OAAI;AAAC,WAAK,WAAW,KAAK,GAAG,GAAGK,EAAC,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,cAAaA,GAAE,YAAW,YAAWA,GAAE,WAAU;AAAC;AAAC,GAAG,UAAU,oBAAkB,GAAG,UAAU,IAAG,GAAG,UAAU,YAAU,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC,GAAE,GAAG,mBAAiB;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,IAAE;AAAC,WAAO,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAG,MAAKK,IAAEL,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEK,IAAEL,IAAEC,IAAE;AAAC,WAAO,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,0BAA0B,GAAE,GAAGA,IAAE,sCAAsC,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACI,IAAEL,OAAI;AAAC,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,UAAU,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAACA,IAAEL,OAAI;AAAC,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,eAAe,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACA,IAAEL,OAAI;AAAC,UAAIC,KAAE,KAAK,YAAWC,KAAED,GAAE;AAAK,YAAME,KAAE,CAAC;AAAE,iBAAUH,MAAKK,IAAE;AAAC,QAAAA,KAAE,GAAGL,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAKK,GAAE,EAAE;AAAE,UAAAJ,GAAE,KAAK,EAAC,OAAM,GAAGD,IAAE,CAAC,KAAG,GAAE,OAAM,GAAG,GAAGA,IAAE,CAAC,GAAE,CAAC,KAAG,IAAG,cAAa,GAAGA,IAAE,CAAC,KAAG,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,GAAE,CAAC;AAAE,QAAAG,GAAE,KAAKF,EAAC;AAAA,MAAC;AAAC,MAAAC,GAAE,KAAKD,IAAE,GAAGE,EAAC,GAAE,GAAG,MAAKH,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC,GAAE,GAAG,mBAAiB;AAAG,IAAI,KAAG,GAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,GAAGK,IAAE;AAAC,EAAAA,GAAE,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,uBAAsB,CAAC,GAAE,mBAAkB,CAAC,GAAE,wBAAuB,CAAC,GAAE,oBAAmB,CAAC,GAAE,yBAAwB,CAAC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,QAAG,CAACA,GAAE;AAAE,aAAOA,GAAE;AAAE,IAAAA,GAAE,EAAEA,GAAE,CAAC;AAAA,EAAC,UAAC;AAAQ,OAAGA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEL,IAAE;AAAC,EAAAK,KAAE,GAAGA,EAAC,GAAEL,GAAE,KAAK,GAAGK,EAAC,CAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,sBAAqB,MAAK,KAAE,GAAE,KAAK,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,uBAAsB,CAAC,GAAE,mBAAkB,CAAC,GAAE,wBAAuB,CAAC,GAAE,oBAAmB,CAAC,GAAE,yBAAwB,CAAC,EAAC,GAAE,KAAK,8BAA4B,KAAK,wBAAsB,OAAG,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,KAAG,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,EAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,iCAAgCA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,+BAA6B,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,2BAA0BA,OAAI,KAAK,wBAAsB,CAAC,CAACA,GAAE,wBAAuB,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,iCAAgCA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,+BAA6B,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,iCAAgCA,OAAI,KAAK,8BAA4B,CAAC,CAACA,GAAE,8BAA6B,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEG,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKG,IAAEF,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,oBAAoB,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,2BAA2B,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,4BAA4B;AAAE,UAAML,KAAE,IAAI,MAAGC,KAAE,IAAI;AAAG,OAAGA,IAAE,GAAE,GAAG,qGAAqG,GAAE,EAAE,GAAE,SAASI,IAAEL,IAAE;AAAC,UAAG,QAAMA;AAAE,YAAG,MAAM,QAAQA,EAAC;AAAE,aAAGK,IAAE,GAAE,GAAGL,IAAE,IAAG,QAAO,QAAO,KAAE,CAAC;AAAA,aAAM;AAAC,cAAG,EAAE,YAAU,OAAOA,MAAGA,cAAa,KAAG,EAAEA,EAAC;AAAG,kBAAM,MAAM,uCAAqCA,KAAE,+EAA+E;AAAE,aAAGK,IAAE,GAAE,GAAGL,IAAE,OAAG,KAAE,GAAE,EAAE,CAAC;AAAA,QAAC;AAAA,IAAC,EAAEC,IAAE,KAAK,EAAE,EAAE,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,oEAAoE,GAAE,GAAGA,IAAE,GAAE,IAAGD,EAAC,GAAE,GAAGC,IAAE,0BAA0B,GAAE,GAAGA,IAAE,+BAA+B,GAAE,GAAGA,IAAE,2CAA2C,GAAE,GAAGA,IAAE,+BAA+B,GAAE,GAAGA,IAAE,yCAAyC,GAAE,GAAGA,IAAE,qDAAqD,GAAE,GAAGA,IAAE,2CAA2C,GAAE,GAAGA,IAAE,uDAAuD,GAAEA,GAAE,EAAEF,EAAC,GAAE,GAAGK,IAAEH,EAAC,GAAE,GAAG,MAAKG,EAAC,GAAE,KAAK,EAAE,oBAAoB,kBAAkB,CAACA,IAAEL,OAAI;AAAC,SAAGK,IAAE,KAAK,EAAE,aAAa,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,wBAAwB,CAACA,IAAEL,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAmB,MAAAI,KAAE,GAAGA,EAAC,GAAEJ,GAAE,KAAK,GAAGI,EAAC,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,gCAA8B,GAAGH,IAAE,+CAA+C,GAAE,GAAG,MAAK,wBAAwB,GAAE,KAAK,EAAE,EAAE,0BAA0B,CAACG,IAAEL,OAAI;AAAC,WAAK,EAAE,wBAAsB,CAAC,GAAG,MAAKK,IAAE,MAAG,CAAC,KAAK,CAAC,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,0BAA0B,CAAAK,OAAG;AAAC,WAAK,EAAE,wBAAsB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,EAAE,oBAAoB,kBAAkB,CAACA,IAAEL,OAAI;AAAC,SAAGK,IAAE,KAAK,EAAE,aAAa,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,0BAAwB,GAAGA,IAAE,mBAAmB,GAAE,GAAGH,IAAE,oCAAoC,GAAE,KAAK,EAAE,oBAAoB,qBAAqB,CAACG,IAAEL,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAgB,WAAK,0BAAwBI,KAAE,GAAGA,EAAC,GAAEJ,GAAE,KAAK,GAAGI,GAAE,EAAE,KAAG,CAAC,CAAC,CAAC,IAAG,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,qBAAqB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,EAAE,oBAAoB,uBAAuB,CAACA,IAAEL,OAAI;AAAC,SAAGK,IAAE,KAAK,EAAE,iBAAiB,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,uBAAuB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,6BAA6B,CAACA,IAAEL,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAuB,MAAAI,KAAE,GAAGA,EAAC,GAAEJ,GAAE,KAAK,GAAGI,EAAC,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,6BAA6B,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,wBAAwB,CAACA,IAAEL,OAAI;AAAC,SAAGK,IAAE,KAAK,EAAE,kBAAkB,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,oBAAoB,8BAA8B,CAACA,IAAEL,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAwB,MAAAI,KAAE,GAAGA,EAAC,GAAEJ,GAAE,KAAK,GAAGI,EAAC,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,8BAA8B,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC,GAAE,GAAG,mBAAiB,IAAG,GAAG,mBAAiB,IAAG,GAAG,sBAAoB,IAAG,GAAG,0BAAwB,IAAG,GAAG,8BAA4B,IAAG,GAAG,2BAAyB,IAAG,GAAG,2BAAyB,IAAG,GAAG,+BAA6B,IAAG,GAAG,4BAA0B,IAAG,GAAG,2BAAyB,IAAG,GAAG,0BAAwB,IAAG,GAAG,6BAA2B;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,eAAc,aAAY,IAAE,GAAE,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,KAAK,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAG,KAAK,GAAE,IAAG,CAAC,CAAC,CAAC,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAG,MAAKK,IAAEL,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,GAAGK,IAAEL,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,aAAa,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,iBAAiB;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,8DAA8D,GAAE,GAAGA,IAAE,mBAAmB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,iCAAiC,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,oBAAoB,mBAAmB,CAACI,IAAEL,OAAI;AAAC,WAAK,IAAE,SAASK,IAAE;AAAC,cAAML,KAAE,EAAC,iBAAgB,GAAGK,IAAE,IAAG,CAAC,EAAE,IAAK,CAAAA,OAAC;AAA7p0H,cAAAK;AAA+p0H,sBAAGA,MAAA,GAAGL,IAAE,IAAG,CAAC,MAAT,gBAAAK,IAAY,QAAK,CAAC,GAAE,GAAG,GAAGL,IAAE,CAAC,GAAE,CAAC,GAAE,GAAGA,IAAE,CAAC,CAAC;AAAA,SAAE,EAAC;AAAE,eAAO,QAAM,GAAG,GAAGA,IAAE,CAAC,CAAC,MAAIL,GAAE,cAAY,GAAG,GAAG,GAAGK,IAAE,CAAC,CAAC,GAAE,CAAC,IAAGL;AAAA,MAAC,EAAE,GAAGK,EAAC,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAmB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,WAAS,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,KAAK,aAAW,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGK,KAAE,KAAK,GAAE,GAAE,GAAEL,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,QAAIL,KAAE,KAAK,GAAEC,KAAE,GAAG,KAAK,GAAE,IAAG,CAAC;AAAE,WAAOA,KAAEA,KAAEA,GAAE,MAAM,IAAE,IAAI,MAAG,WAASI,GAAE,cAAY,GAAGJ,IAAE,GAAEI,GAAE,WAAW,IAAE,iBAAgBA,MAAG,GAAGJ,IAAE,CAAC,GAAE,WAASI,GAAE,WAAS,GAAGJ,IAAE,GAAEI,GAAE,QAAQ,IAAE,cAAaA,MAAG,GAAGJ,IAAE,CAAC,GAAE,GAAGD,IAAE,GAAE,GAAEC,EAAC,GAAE,KAAK,EAAEI,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAE;AAAC,WAAO,GAAG,MAAKK,IAAEL,EAAC,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,GAAGK,IAAEL,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,0DAA0D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,2BAA2B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,oBAAoB,kBAAkB,CAACI,IAAEL,OAAI;AAAC,MAAAK,KAAE,GAAGA,EAAC,GAAE,KAAK,aAAW,SAASA,IAAE;AAAC,eAAM,EAAC,YAAW,GAAGA,IAAE,IAAG,CAAC,EAAE,IAAK,CAAAA,OAAG;AAA3r3H,cAAAK,KAAA;AAA4r3H,gBAAMV,KAAE,EAAC,WAAU,GAAG,GAAGK,IAAE,CAAC,GAAE,CAAC,KAAG,IAAG,UAAS,GAAGA,IAAE,CAAC,KAAG,GAAE;AAAE,cAAG,WAAS,GAAGA,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC;AAAE,YAAAA,KAAE,GAAGA,KAAE,GAAGA,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,GAAE,GAAE,IAAG,GAAG,CAAC,GAAEL,GAAE,iBAAeK,GAAE,MAAM;AAAA,eAAM;AAAC,kBAAMJ,KAAE,IAAI,WAAW,CAAC;AAAE,YAAAD,GAAE,uBAAmB,MAAAU,MAAA,GAAGL,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,MAAf,gBAAAK,IAAkB,SAAlB,mBAAwB,SAAMT;AAAA,UAAC;AAAC,iBAAOD;AAAA,QAAC,CAAE,GAAE,aAAY,GAAG,GAAG,GAAGK,IAAE,CAAC,CAAC,GAAE,CAAC,EAAC;AAAA,MAAC,EAAEA,EAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,mBAAiB,SAASA,IAAEL,IAAE;AAAC,MAAGK,GAAE,kBAAgBL,GAAE;AAAe,IAAAK,KAAE,GAAGA,GAAE,gBAAeL,GAAE,cAAc;AAAA,OAAM;AAAC,QAAG,CAACK,GAAE,sBAAoB,CAACL,GAAE;AAAmB,YAAM,MAAM,0EAA0E;AAAE,IAAAK,KAAE,GAAG,GAAGA,GAAE,kBAAkB,GAAE,GAAGL,GAAE,kBAAkB,CAAC;AAAA,EAAC;AAAC,SAAOK;AAAC,GAAE,GAAG,UAAU,gBAAc,GAAG,UAAU,IAAG,GAAG,UAAU,QAAM,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYK,IAAEL,IAAEC,IAAE;AAAC,SAAK,kBAAgBI,IAAE,KAAK,eAAaL,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAz15H,QAAAS,KAAA;AAA015H,KAAAA,MAAA,KAAK,oBAAL,gBAAAA,IAAsB,QAAS,CAAAL,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,KAAI,UAAK,iBAAL,mBAAmB;AAAA,EAAO;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,EAAAA,GAAE,eAAa,QAAOA,GAAE,kBAAgB,QAAOA,GAAE,gBAAc;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,UAAML,KAAE,IAAI,GAAGK,GAAE,iBAAgBA,GAAE,cAAaA,GAAE,aAAa;AAAE,QAAG,CAACA,GAAE;AAAE,aAAOL;AAAE,IAAAK,GAAE,EAAEL,EAAC;AAAA,EAAC,UAAC;AAAQ,OAAGK,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,qBAAmB,OAAG,KAAK,wBAAsB,MAAG,KAAK,IAAE,IAAI,MAAG,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAGK,KAAE,KAAK,GAAE,GAAE,GAAEL,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,WAASA,GAAE,qBAAmB,GAAG,KAAK,GAAE,GAAE,GAAGA,GAAE,kBAAkB,CAAC,IAAE,wBAAuBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,wBAAuBA,OAAI,KAAK,qBAAmBA,GAAE,sBAAoB,QAAI,2BAA0BA,OAAI,KAAK,wBAAsBA,GAAE,yBAAuB,OAAI,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,KAAC,SAASA,IAAE;AAApw7H,UAAAK,KAAA;AAAqw7H,YAAMV,KAAE,GAAGK,GAAE,GAAG,GAAE,IAAG,CAAC,EAAE,OAAQ,CAAAA,OAAG,GAAGA,IAAE,CAAC,EAAE,SAAS,iDAAiD,CAAE;AAAE,UAAGA,GAAE,IAAE,CAAC,GAAEL,GAAE,SAAO;AAAE,cAAM,MAAM,8EAA8E;AAAE,YAAIA,GAAE,aAAS,MAAAU,MAAA,GAAGV,GAAE,CAAC,GAAE,IAAG,CAAC,MAAZ,gBAAAU,IAAe,QAAf,mBAAoB,QAAK,oBAAI,OAAK,QAAS,CAACV,IAAEC,OAAI;AAAC,QAAAI,GAAE,EAAE,OAAOJ,EAAC,CAAC,IAAE,GAAGD,IAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGK,IAAEL,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGG,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKG,IAAEF,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,GAAG,MAAKI,EAAC,GAAE,KAAK,0BAAwB,GAAGA,IAAE,kBAAkB,GAAE,GAAGJ,IAAE,mCAAmC,GAAE,GAAG,MAAK,kBAAkB,GAAE,KAAK,EAAE,GAAG,oBAAoB,CAACI,IAAEL,OAAI;AAAC,WAAK,kBAAgBK,GAAE,IAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,CAAE,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAoB,CAAAK,OAAG;AAAC,WAAK,kBAAgB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,uBAAqB,GAAGA,IAAE,eAAe,GAAE,GAAGJ,IAAE,6BAA6B,GAAE,GAAG,MAAK,eAAe,GAAE,KAAK,EAAE,EAAE,iBAAiB,CAACI,IAAEL,OAAI;AAAC,WAAK,eAAa,GAAG,MAAKK,IAAE,OAAG,CAAC,KAAK,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAK,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,GAAGA,IAAE,gBAAgB,GAAE,GAAGJ,IAAE,+BAA+B,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACI,IAAEL,OAAI;AAAC,WAAK,gBAAcK,IAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,YAAU,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,UAAQ,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYK,IAAEL,IAAEC,IAAE;AAAC,SAAK,kBAAgBI,IAAE,KAAK,eAAaL,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,QAAO;AAA/6/H,QAAAS,KAAA;AAAg7/H,KAAAA,MAAA,KAAK,oBAAL,gBAAAA,IAAsB,QAAS,CAAAL,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,KAAI,UAAK,iBAAL,mBAAmB;AAAA,EAAO;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,CAAC,GAAE,IAAG,EAAE;AAA7D,IAA+D,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAAnF,IAAqF,KAAG,CAAC,GAAE,EAAE;AAA7F,IAA+F,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA7G,IAA+G,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA3J,IAA6J,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA3K,IAA6K,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAvN,IAAyN,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAArQ,IAAuQ,KAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,EAAE;AAArS,IAAuS,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,gBAAe,KAAE,GAAE,KAAK,qBAAmB,OAAG,KAAK,wBAAsB,MAAG,KAAK,IAAE,IAAI,MAAG,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAGK,KAAE,KAAK,GAAE,GAAE,GAAEL,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,wBAAuBA,OAAI,KAAK,qBAAmBA,GAAE,sBAAoB,QAAI,2BAA0BA,OAAI,KAAK,wBAAsBA,GAAE,yBAAuB,OAAI,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,SAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,KAAK,gBAAc,KAAK,eAAa,KAAK,kBAAgB,QAAOD,KAAE,KAAK,IAAE,GAAEC,KAAE,IAAI;AAAG,UAAME,KAAE,IAAI;AAAG,QAAIE,KAAE,IAAI;AAAG,QAAG,GAAGA,IAAE,GAAE,GAAG,GAAE,GAAGF,IAAE,GAAE,IAAGE,EAAC,GAAEN,GAAE,YAAUA,GAAE;AAAS,YAAM,MAAM,4CAA4C;AAAE,QAAGA,GAAE,UAAS;AAAC,UAAIO,KAAE,IAAI;AAAG,SAAGA,IAAE,GAAE,IAAE,GAAE,GAAGA,IAAE,GAAEP,GAAE,SAAS,CAAC,GAAE,GAAGO,IAAE,GAAEP,GAAE,SAAS,CAAC,GAAE,GAAGI,IAAE,GAAE,IAAGG,EAAC;AAAA,IAAC,OAAK;AAAC,UAAG,CAACP,GAAE;AAAS,cAAM,MAAM,+CAA+C;AAAE,WAAIO,OAAKD,KAAE,IAAI,MAAGN,GAAE;AAAU,WAAGA,KAAE,IAAI,MAAG,GAAE,IAAE,GAAE,GAAGA,IAAE,GAAEO,GAAE,CAAC,GAAE,GAAGP,IAAE,GAAEO,GAAE,CAAC,GAAE,GAAGD,IAAE,GAAE,IAAGN,EAAC;AAAE,SAAGI,IAAE,IAAG,IAAGE,EAAC;AAAA,IAAC;AAAC,OAAGJ,IAAE,GAAE,IAAGE,EAAC,GAAE,KAAK,EAAE,iBAAiBF,GAAE,EAAE,GAAE,sBAAqB,UAASD,EAAC,GAAE,GAAG,MAAKI,IAAEF,EAAC;AAAE,OAAE;AAAC,UAAG;AAAC,cAAME,KAAE,IAAI,GAAG,KAAK,iBAAgB,KAAK,cAAa,KAAK,aAAa;AAAE,YAAG,CAAC,KAAK,GAAE;AAAC,cAAIG,KAAEH;AAAE,gBAAM;AAAA,QAAC;AAAC,aAAK,EAAEA,EAAC;AAAA,MAAC,UAAC;AAAQ,WAAG,IAAI;AAAA,MAAC;AAAC,MAAAG,KAAE;AAAA,IAAM;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIH,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,QAAQ,GAAE,GAAGA,IAAE,cAAc;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wEAAwE,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,YAAY,GAAE,GAAGA,IAAE,wBAAwB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,GAAG,MAAKI,EAAC,GAAE,KAAK,0BAAwB,GAAGA,IAAE,kBAAkB,GAAE,GAAGJ,IAAE,mCAAmC,GAAE,GAAG,MAAK,kBAAkB,GAAE,KAAK,EAAE,GAAG,oBAAoB,CAACI,IAAEL,OAAI;AAAC,WAAK,kBAAgBK,GAAE,IAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,CAAE,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAoB,CAAAK,OAAG;AAAC,WAAK,kBAAgB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,KAAK,uBAAqB,GAAGA,IAAE,eAAe,GAAE,GAAGJ,IAAE,6BAA6B,GAAE,GAAG,MAAK,eAAe,GAAE,KAAK,EAAE,EAAE,iBAAiB,CAACI,IAAEL,OAAI;AAAC,WAAK,eAAa,GAAG,MAAKK,IAAE,OAAG,CAAC,KAAK,CAAC,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,iBAAiB,CAAAK,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAG,GAAGA,IAAE,gBAAgB,GAAE,GAAGJ,IAAE,+BAA+B,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAACI,IAAEL,OAAI;AAAC,WAAK,gBAAcK,IAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAkB,CAAAK,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,UAAQ,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,mBAAkB,aAAY,KAAE,GAAE,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,WAASA,GAAE,qBAAmB,GAAG,KAAK,GAAE,GAAE,GAAGA,GAAE,kBAAkB,CAAC,IAAE,wBAAuBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,aAAW,GAAG,KAAK,GAAE,GAAEA,GAAE,UAAU,IAAE,gBAAeA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,iBAAe,GAAG,KAAK,GAAE,GAAEA,GAAE,cAAc,IAAE,oBAAmBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,oBAAkB,GAAG,KAAK,GAAE,GAAEA,GAAE,iBAAiB,IAAE,uBAAsBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,mBAAiB,GAAG,KAAK,GAAE,GAAEA,GAAE,gBAAgB,IAAE,sBAAqBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKK,IAAEL,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAEK,IAAEL,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKI,IAAEJ,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,iBAAiB,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4CAA4C,GAAE,GAAGA,IAAE,uBAAuB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAACI,IAAEL,OAAI;AAAC,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,EAAE,WAAW,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,cAAc,CAAAK,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,eAAeA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYK,IAAEL,IAAEC,IAAE;AAAC,SAAK,YAAUI,IAAE,KAAK,iBAAeL,IAAE,KAAK,oBAAkBC;AAAA,EAAC;AAAA,EAAC,QAAO;AAA/mqI,QAAAS;AAAgnqI,KAAAA,MAAA,KAAK,sBAAL,gBAAAA,IAAwB,QAAS,CAAAL,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC;AAAA,EAAG;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,EAAAA,GAAE,YAAU,CAAC,GAAEA,GAAE,iBAAe,CAAC,GAAEA,GAAE,oBAAkB;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,UAAML,KAAE,IAAI,GAAGK,GAAE,WAAUA,GAAE,gBAAeA,GAAE,iBAAiB;AAAE,QAAG,CAACA,GAAE;AAAE,aAAOL;AAAE,IAAAK,GAAE,EAAEL,EAAC;AAAA,EAAC,UAAC;AAAQ,OAAGK,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEL,IAAE;AAAC,UAAM,IAAI,GAAGK,IAAEL,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,0BAAwB,OAAG,GAAGK,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEL,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYK,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,6BAA4BA,OAAI,KAAK,0BAAwBA,GAAE,2BAAyB,QAAI,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEL,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEG,IAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKG,IAAEF,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIK,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,iBAAiB,GAAE,GAAGA,IAAE,oBAAoB;AAAE,UAAML,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,qCAAqC,GAAE,GAAGA,IAAE,iCAAiC,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGK,IAAEJ,EAAC,GAAE,GAAG,MAAKI,EAAC,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAACA,IAAEL,OAAI;AAAC,WAAK,YAAU,CAAC;AAAE,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,UAAU,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAwB,CAAAK,OAAG;AAAC,WAAK,YAAU,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAmB,CAACA,IAAEL,OAAI;AAAC,WAAK,iBAAe,CAAC;AAAE,iBAAUA,MAAKK;AAAE,QAAAA,KAAE,GAAGL,EAAC,GAAE,KAAK,eAAe,KAAK,GAAGK,EAAC,CAAC;AAAE,SAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAmB,CAAAK,OAAG;AAAC,WAAK,iBAAe,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,4BAA0B,GAAGJ,IAAE,sCAAsC,GAAE,GAAG,MAAK,oBAAoB,GAAE,KAAK,EAAE,GAAG,sBAAsB,CAACI,IAAEL,OAAI;AAAC,WAAK,oBAAkBK,GAAE,IAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,CAAE,GAAE,GAAG,MAAKL,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,EAAE,0BAA0B,sBAAsB,CAAAK,OAAG;AAAC,WAAK,oBAAkB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,CAAE,IAAGA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,gBAAeL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAE,EAAC,aAAY,EAAC,kBAAiBL,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASK,IAAEL,IAAE;AAAC,SAAO,GAAG,IAAGK,IAAEL,EAAC;AAAC,GAAE,GAAG,mBAAiB;", "names": ["e", "n", "r", "i", "s", "t", "o", "a", "h", "c", "_a", "u", "l", "f", "d", "_e"]}