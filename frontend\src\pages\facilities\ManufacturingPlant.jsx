import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Link } from 'react-router-dom';

const ManufacturingPlant = () => {
  useEffect(() => {
    document.title = 'Manufacturing Plant - KJL Industries';
  }, []);

  const features = [
    {
      title: 'Advanced CNC Machines',
      description: 'State-of-the-art computer numerical control machines for precision manufacturing',
      icon: '⚙️'
    },
    {
      title: 'Automated Assembly Lines',
      description: 'Fully automated production lines with robotic integration for efficiency',
      icon: '🏭'
    },
    {
      title: 'Quality Control Lab',
      description: 'Integrated quality assurance systems with real-time monitoring',
      icon: '🔍'
    },
    {
      title: 'Research & Development Center',
      description: 'Innovation hub for developing next-generation manufacturing processes',
      icon: '🧪'
    },
    {
      title: 'Environmental Controls',
      description: 'Climate-controlled environment ensuring optimal production conditions',
      icon: '🌡️'
    },
    {
      title: 'Safety Systems',
      description: 'Comprehensive safety protocols and emergency response systems',
      icon: '🛡️'
    }
  ];

  const specifications = [
    { label: 'Total Floor Area', value: '50,000 sq ft' },
    { label: 'Production Capacity', value: '10,000 units/month' },
    { label: 'CNC Machines', value: '25 units' },
    { label: 'Assembly Lines', value: '8 lines' },
    { label: 'Quality Stations', value: '12 stations' },
    { label: 'Operating Hours', value: '24/7' }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Back Button */}
      <div className="container-custom py-6">
        <Link 
          to="/facilities" 
          className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-300 group"
        >
          <svg 
            className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Facilities
        </Link>
      </div>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-white via-gray-50 to-gray-100">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🏭</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-display"
                style={{
                  background: 'linear-gradient(135deg, #1f2937 0%, #374151 30%, #6b7280 70%, #9ca3af 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                }}>
              Manufacturing Plant
            </h1>
            <p className="text-gray-700 text-lg md:text-xl leading-relaxed max-w-3xl mx-auto">
              Our state-of-the-art manufacturing facility combines cutting-edge technology with 
              precision engineering to deliver exceptional products that meet the highest industry standards.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Specifications Grid */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Plant Specifications</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Key metrics and capabilities of our manufacturing facility
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {specifications.map((spec, index) => (
              <motion.div
                key={spec.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6 text-center hover:bg-gray-100 transition-colors duration-300"
              >
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2 font-display">
                  {spec.value}
                </h3>
                <p className="text-gray-600 font-medium">{spec.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 font-display mb-4">
              Advanced Manufacturing Capabilities
            </h2>
            <p className="text-gray-700 text-lg max-w-3xl mx-auto">
              Our manufacturing plant is equipped with the latest technology and systems 
              to ensure precision, efficiency, and quality in every product we create.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ 
                  scale: 1.03,
                  y: -5
                }}
                className="bg-white rounded-2xl p-8 shadow-soft hover:shadow-medium transition-all duration-300 border border-gray-200 group"
              >
                <div className="text-4xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-display mb-4 group-hover:text-gray-700 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why It Matters Section */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 md:p-12"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mr-4">
                  <span className="text-2xl">💡</span>
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 font-display">Why It Matters</h2>
              </div>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                Our manufacturing plant represents the pinnacle of modern industrial engineering. 
                By integrating advanced automation, precision machinery, and rigorous quality control, 
                we ensure that every product meets the exacting standards our clients demand.
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-soft">
                  <h3 className="font-bold text-gray-900 mb-2">Precision & Quality</h3>
                  <p className="text-gray-600">
                    Advanced CNC machines and quality control systems ensure every component 
                    meets exact specifications with minimal tolerance.
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-soft">
                  <h3 className="font-bold text-gray-900 mb-2">Efficiency & Scale</h3>
                  <p className="text-gray-600">
                    Automated assembly lines and optimized workflows enable us to deliver 
                    large-scale production without compromising quality.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-gray-100 to-gray-200">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">
              Ready to Experience Our Manufacturing Excellence?
            </h2>
            <p className="text-gray-700 text-lg mb-8 max-w-2xl mx-auto">
              Contact us to learn more about our manufacturing capabilities and how we can bring your projects to life.
            </p>
            <Link 
              to="/contact"
              className="inline-flex items-center px-8 py-4 bg-gray-900 text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
            >
              Get in Touch
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ManufacturingPlant;
